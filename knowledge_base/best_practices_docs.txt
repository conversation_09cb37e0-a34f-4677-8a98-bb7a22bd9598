# Blender 3D Modeling Best Practices - Enhanced Version
# Version: 2.0.0 - Expanded for Task 4.4
# Last Updated: 2025-07-19

## General 3D Modeling Best Practices

### Scene Organization
Proper scene organization is crucial for maintainable and efficient 3D projects.

#### Collection Management
```python
import bpy

# Create organized collections
def create_project_structure():
    # Main collections
    geometry_col = bpy.data.collections.new("Geometry")
    lights_col = bpy.data.collections.new("Lights")
    cameras_col = bpy.data.collections.new("Cameras")
    materials_col = bpy.data.collections.new("Materials")
    
    # Link to scene
    scene = bpy.context.scene
    scene.collection.children.link(geometry_col)
    scene.collection.children.link(lights_col)
    scene.collection.children.link(cameras_col)
    
    # Sub-collections for geometry
    props_col = bpy.data.collections.new("Props")
    characters_col = bpy.data.collections.new("Characters")
    environment_col = bpy.data.collections.new("Environment")
    
    geometry_col.children.link(props_col)
    geometry_col.children.link(characters_col)
    geometry_col.children.link(environment_col)
```

#### Naming Conventions
```python
import bpy

# Consistent naming patterns
def apply_naming_conventions():
    # Object naming: Type_Description_Number
    # Examples: MESH_Chair_01, LIGHT_Key_01, CAM_Main_01
    
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH':
            if not obj.name.startswith('MESH_'):
                obj.name = f"MESH_{obj.name}"
        elif obj.type == 'LIGHT':
            if not obj.name.startswith('LIGHT_'):
                obj.name = f"LIGHT_{obj.name}"
        elif obj.type == 'CAMERA':
            if not obj.name.startswith('CAM_'):
                obj.name = f"CAM_{obj.name}"
```

### Mesh Topology Best Practices

#### Quad-Based Modeling
```python
import bpy
import bmesh

def ensure_quad_topology():
    """Convert triangles to quads where possible."""
    obj = bpy.context.active_object
    if obj and obj.type == 'MESH':
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.mode_set(mode='EDIT')
        
        # Select all faces
        bpy.ops.mesh.select_all(action='SELECT')
        
        # Convert tris to quads
        bpy.ops.mesh.tris_convert_to_quads(
            face_threshold=0.698132,  # 40 degrees
            shape_threshold=0.698132
        )
        
        bpy.ops.object.mode_set(mode='OBJECT')
```

#### Edge Flow Optimization
```python
import bpy

def optimize_edge_flow():
    """Optimize edge flow for better deformation."""
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Select all edges
    bpy.ops.mesh.select_all(action='SELECT')
    
    # Smooth vertices
    bpy.ops.mesh.vertices_smooth(factor=0.5, repeat=2)
    
    # Relax edges
    bpy.ops.mesh.loopcut_slide(
        MESH_OT_loopcut={"number_cuts": 1},
        TRANSFORM_OT_edge_slide={"value": 0}
    )
    
    bpy.ops.object.mode_set(mode='OBJECT')
```

### Performance Optimization

#### Level of Detail (LOD) Management
```python
import bpy

def create_lod_system(base_object, lod_levels=[0.8, 0.5, 0.2]):
    """Create multiple LOD levels for an object."""
    lod_objects = []
    
    for i, ratio in enumerate(lod_levels):
        # Duplicate object
        bpy.context.view_layer.objects.active = base_object
        bpy.ops.object.duplicate()
        lod_obj = bpy.context.active_object
        lod_obj.name = f"{base_object.name}_LOD_{i+1}"
        
        # Add decimate modifier
        decimate_mod = lod_obj.modifiers.new(name="Decimate", type='DECIMATE')
        decimate_mod.ratio = ratio
        
        # Apply modifier
        bpy.context.view_layer.objects.active = lod_obj
        bpy.ops.object.modifier_apply(modifier="Decimate")
        
        lod_objects.append(lod_obj)
    
    return lod_objects
```

#### Efficient Material Usage
```python
import bpy

def optimize_materials():
    """Optimize materials for better performance."""
    # Remove unused materials
    for material in bpy.data.materials:
        if material.users == 0:
            bpy.data.materials.remove(material)
    
    # Merge similar materials
    materials_to_merge = {}
    for material in bpy.data.materials:
        # Simple hash based on diffuse color
        if hasattr(material.node_tree, 'nodes'):
            principled = material.node_tree.nodes.get('Principled BSDF')
            if principled:
                color = tuple(principled.inputs['Base Color'].default_value[:3])
                color_key = tuple(round(c, 2) for c in color)
                
                if color_key in materials_to_merge:
                    # Replace material usage
                    target_material = materials_to_merge[color_key]
                    for obj in bpy.data.objects:
                        if obj.type == 'MESH':
                            for slot in obj.material_slots:
                                if slot.material == material:
                                    slot.material = target_material
                else:
                    materials_to_merge[color_key] = material
```

### Animation Best Practices

#### Keyframe Optimization
```python
import bpy

def optimize_keyframes():
    """Optimize keyframe curves for better performance."""
    for obj in bpy.context.scene.objects:
        if obj.animation_data and obj.animation_data.action:
            action = obj.animation_data.action
            
            for fcurve in action.fcurves:
                # Remove redundant keyframes
                bpy.context.area.type = 'GRAPH_EDITOR'
                bpy.context.space_data.dopesheet.filter_text = obj.name
                
                # Select all keyframes
                for keyframe in fcurve.keyframe_points:
                    keyframe.select_control_point = True
                
                # Decimate keyframes
                bpy.ops.graph.decimate(mode='RATIO', factor=0.1)
```

#### Constraint Setup
```python
import bpy

def setup_efficient_constraints():
    """Set up constraints for efficient animation."""
    obj = bpy.context.active_object
    
    if obj and obj.type == 'ARMATURE':
        # Add IK constraints to bones
        bpy.ops.object.mode_set(mode='POSE')
        
        for bone in obj.pose.bones:
            if bone.name.endswith('_IK'):
                # Add IK constraint
                ik_constraint = bone.constraints.new(type='IK')
                ik_constraint.target = obj
                ik_constraint.subtarget = bone.name.replace('_IK', '_target')
                ik_constraint.chain_count = 2
        
        bpy.ops.object.mode_set(mode='OBJECT')
```

### Rendering Optimization

#### Efficient Lighting Setup
```python
import bpy

def setup_efficient_lighting():
    """Set up efficient lighting for rendering."""
    # Remove default light
    if 'Light' in bpy.data.objects:
        bpy.data.objects.remove(bpy.data.objects['Light'], do_unlink=True)
    
    # Add key light
    bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
    key_light = bpy.context.active_object
    key_light.name = "Key_Light"
    key_light.data.energy = 3.0
    
    # Add fill light
    bpy.ops.object.light_add(type='AREA', location=(-3, 2, 5))
    fill_light = bpy.context.active_object
    fill_light.name = "Fill_Light"
    fill_light.data.energy = 1.0
    fill_light.data.size = 2.0
    
    # Add rim light
    bpy.ops.object.light_add(type='SPOT', location=(0, -5, 3))
    rim_light = bpy.context.active_object
    rim_light.name = "Rim_Light"
    rim_light.data.energy = 2.0
    rim_light.data.spot_size = 1.2
```

#### Render Settings Optimization
```python
import bpy

def optimize_render_settings():
    """Optimize render settings for quality and speed."""
    scene = bpy.context.scene
    
    # Set render engine
    scene.render.engine = 'CYCLES'
    
    # Optimize sampling
    scene.cycles.samples = 128  # Balanced quality/speed
    scene.cycles.adaptive_threshold = 0.01
    scene.cycles.use_adaptive_sampling = True
    
    # Enable denoising
    scene.cycles.use_denoising = True
    scene.cycles.denoiser = 'OPTIX'  # If available
    
    # Optimize tile size
    scene.render.tile_x = 256
    scene.render.tile_y = 256
    
    # Set output format
    scene.render.image_settings.file_format = 'PNG'
    scene.render.image_settings.compression = 15
```

### Error Prevention and Debugging

#### Common Error Checks
```python
import bpy

def check_common_errors():
    """Check for common modeling errors."""
    errors = []
    
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH':
            mesh = obj.data
            
            # Check for non-manifold geometry
            bpy.context.view_layer.objects.active = obj
            bpy.ops.object.mode_set(mode='EDIT')
            bpy.ops.mesh.select_all(action='DESELECT')
            bpy.ops.mesh.select_non_manifold()
            
            selected_verts = [v for v in mesh.vertices if v.select]
            if selected_verts:
                errors.append(f"{obj.name}: Non-manifold geometry detected")
            
            # Check for zero-area faces
            bpy.ops.mesh.select_all(action='DESELECT')
            bpy.ops.mesh.select_face_by_sides(number=3, type='EQUAL')
            
            # Check scale
            if any(abs(s) < 0.001 for s in obj.scale):
                errors.append(f"{obj.name}: Extreme scale values detected")
            
            bpy.ops.object.mode_set(mode='OBJECT')
    
    return errors
```

#### Automated Cleanup
```python
import bpy

def automated_cleanup():
    """Perform automated scene cleanup."""
    # Remove orphaned data
    bpy.ops.outliner.orphans_purge(do_local_ids=True, do_linked_ids=True, do_recursive=True)
    
    # Clean up meshes
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH':
            bpy.context.view_layer.objects.active = obj
            bpy.ops.object.mode_set(mode='EDIT')
            
            # Remove doubles
            bpy.ops.mesh.select_all(action='SELECT')
            bpy.ops.mesh.remove_doubles(threshold=0.001)
            
            # Recalculate normals
            bpy.ops.mesh.normals_make_consistent(inside=False)
            
            bpy.ops.object.mode_set(mode='OBJECT')
```

### Memory Management

#### Efficient Data Handling
```python
import bpy

def manage_memory_efficiently():
    """Manage memory usage efficiently."""
    # Clear unused images
    for image in bpy.data.images:
        if image.users == 0:
            bpy.data.images.remove(image)
    
    # Pack external files
    bpy.ops.file.pack_all()
    
    # Optimize texture sizes
    for image in bpy.data.images:
        if image.size[0] > 2048 or image.size[1] > 2048:
            # Scale down large textures
            image.scale(2048, 2048)
```

### Version Control Best Practices

#### File Organization
```python
import bpy
import os

def setup_version_control():
    """Set up proper file organization for version control."""
    # Save incremental versions
    filepath = bpy.data.filepath
    if filepath:
        base_name = os.path.splitext(filepath)[0]
        version_num = 1
        
        # Find next version number
        while os.path.exists(f"{base_name}_v{version_num:03d}.blend"):
            version_num += 1
        
        new_filepath = f"{base_name}_v{version_num:03d}.blend"
        bpy.ops.wm.save_as_mainfile(filepath=new_filepath)
```

## Molecular Modeling Specific Best Practices

### Atom Representation
- Use appropriate atomic radii for the visualization purpose
- Consider the target audience when choosing representation style
- Maintain consistent color schemes across related structures

### Bond Visualization
- Use standard bond lengths and angles when possible
- Represent bond order clearly (single, double, triple)
- Consider using transparency for overlapping structures

### Large Structure Handling
- Use instancing for repeated molecular units
- Implement level-of-detail for complex proteins
- Consider using simplified representations for distant objects

### Animation Guidelines
- Use realistic molecular motion when animating
- Consider the time scale of molecular processes
- Provide clear visual cues for important interactions

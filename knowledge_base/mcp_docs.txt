# Molecular Nodes (MCP) Documentation - Enhanced Version
# Version: 2.0.0 - Expanded for Task 4.4
# Last Updated: 2025-07-19

## Introduction to Molecular Nodes

Molecular Nodes is a powerful Blender add-on that enables the creation and visualization of molecular structures, proteins, and chemical compounds within <PERSON>lender's 3D environment.

### Key Features
- Import molecular structures from PDB, SDF, MOL files
- Create atoms, bonds, and molecular assemblies
- Advanced visualization techniques for proteins and DNA
- Integration with Blender's material and animation systems
- Support for large molecular datasets

## Basic Molecular Operations

### mn.add_atom
Create individual atoms in the scene.

Parameters:
- element: str - Chemical element symbol (e.g., 'C', 'N', 'O', 'H')
- location: Vector - 3D position for the atom
- radius: float, default varies by element - Atomic radius
- color: Vector, default varies by element - Atom color (RGB)

Example:
```python
import bpy
# Create carbon atom
bpy.ops.mn.add_atom(element='C', location=(0, 0, 0))

# Create oxygen atom with custom properties
bpy.ops.mn.add_atom(
    element='O', 
    location=(1.5, 0, 0),
    radius=0.8,
    color=(1.0, 0.0, 0.0)
)

# Create nitrogen atom
bpy.ops.mn.add_atom(element='N', location=(0, 1.5, 0))
```

### mn.create_bond
Create bonds between atoms.

Parameters:
- bond_type: str - Type of bond ('SINGLE', 'DOUBLE', 'TRIPLE', 'AROMATIC')
- atom1: Object - First atom object
- atom2: Object - Second atom object
- bond_length: float, optional - Custom bond length

Example:
```python
import bpy
# Create single bond between two selected atoms
bpy.ops.mn.create_bond(bond_type='SINGLE')

# Create double bond with custom length
bpy.ops.mn.create_bond(
    bond_type='DOUBLE',
    bond_length=1.2
)
```

### mn.add_molecule
Create predefined molecular structures.

Parameters:
- molecule_type: str - Type of molecule ('WATER', 'METHANE', 'BENZENE', 'GLUCOSE')
- location: Vector - Position for the molecule
- scale: float, default 1.0 - Scale factor

Example:
```python
import bpy
# Create water molecule
bpy.ops.mn.add_molecule(
    molecule_type='WATER',
    location=(0, 0, 0),
    scale=1.0
)

# Create benzene ring
bpy.ops.mn.add_molecule(
    molecule_type='BENZENE',
    location=(5, 0, 0),
    scale=1.5
)
```

## File Import Operations

### mn.import_pdb
Import protein structures from PDB files.

Parameters:
- filepath: str - Path to PDB file
- import_style: str - Import style ('ATOMS', 'CARTOON', 'SURFACE')
- chain_selection: list - Specific chains to import
- show_hydrogens: bool, default False - Include hydrogen atoms

Example:
```python
import bpy
# Import PDB file with cartoon representation
bpy.ops.mn.import_pdb(
    filepath="/path/to/protein.pdb",
    import_style='CARTOON',
    show_hydrogens=False
)

# Import specific chains only
bpy.ops.mn.import_pdb(
    filepath="/path/to/complex.pdb",
    import_style='ATOMS',
    chain_selection=['A', 'B']
)
```

### mn.import_sdf
Import molecular structures from SDF files.

Parameters:
- filepath: str - Path to SDF file
- multi_model: bool, default False - Import multiple models
- frame_step: int, default 1 - Frame step for animations

Example:
```python
import bpy
# Import SDF file
bpy.ops.mn.import_sdf(
    filepath="/path/to/molecule.sdf",
    multi_model=True,
    frame_step=5
)
```

## Protein Visualization

### mn.create_cartoon
Create cartoon representation of protein backbone.

Parameters:
- selection: str - Atom selection criteria
- cartoon_type: str - Type of cartoon ('RIBBON', 'TUBE', 'ARROW')
- width: float, default 1.0 - Cartoon width
- smoothness: int, default 3 - Smoothness level

Example:
```python
import bpy
# Create ribbon cartoon for entire protein
bpy.ops.mn.create_cartoon(
    selection='all',
    cartoon_type='RIBBON',
    width=1.2,
    smoothness=4
)

# Create tube cartoon for alpha helices
bpy.ops.mn.create_cartoon(
    selection='helix',
    cartoon_type='TUBE',
    width=0.8
)
```

### mn.create_surface
Generate molecular surface representation.

Parameters:
- surface_type: str - Surface type ('VDW', 'SOLVENT', 'MOLECULAR')
- probe_radius: float, default 1.4 - Probe radius for solvent surface
- resolution: float, default 0.5 - Surface resolution

Example:
```python
import bpy
# Create van der Waals surface
bpy.ops.mn.create_surface(
    surface_type='VDW',
    resolution=0.3
)

# Create solvent accessible surface
bpy.ops.mn.create_surface(
    surface_type='SOLVENT',
    probe_radius=1.4,
    resolution=0.5
)
```

## DNA and RNA Structures

### mn.create_dna_helix
Create DNA double helix structure.

Parameters:
- sequence: str - DNA sequence (A, T, G, C)
- helix_type: str - Helix type ('B_FORM', 'A_FORM', 'Z_FORM')
- base_pairs: int - Number of base pairs
- pitch: float, default 3.4 - Helix pitch

Example:
```python
import bpy
# Create B-form DNA helix
bpy.ops.mn.create_dna_helix(
    sequence='ATCGATCG',
    helix_type='B_FORM',
    base_pairs=20,
    pitch=3.4
)
```

### mn.create_rna_structure
Create RNA secondary structure.

Parameters:
- sequence: str - RNA sequence (A, U, G, C)
- structure_type: str - Structure type ('HAIRPIN', 'STEM_LOOP', 'BULGE')
- folding_temperature: float, default 37.0 - Folding temperature

Example:
```python
import bpy
# Create RNA hairpin structure
bpy.ops.mn.create_rna_structure(
    sequence='GGCCAAUUGGCC',
    structure_type='HAIRPIN',
    folding_temperature=37.0
)
```

## Advanced Molecular Features

### mn.calculate_properties
Calculate molecular properties.

Parameters:
- property_type: str - Property to calculate ('MASS', 'VOLUME', 'SURFACE_AREA')
- selection: str - Atom selection

Example:
```python
import bpy
# Calculate molecular mass
mass = bpy.ops.mn.calculate_properties(
    property_type='MASS',
    selection='all'
)

# Calculate surface area
surface_area = bpy.ops.mn.calculate_properties(
    property_type='SURFACE_AREA',
    selection='protein'
)
```

### mn.animate_trajectory
Animate molecular dynamics trajectory.

Parameters:
- trajectory_file: str - Path to trajectory file
- frame_range: tuple - Start and end frames
- interpolation: str - Interpolation method ('LINEAR', 'BEZIER')

Example:
```python
import bpy
# Animate MD trajectory
bpy.ops.mn.animate_trajectory(
    trajectory_file="/path/to/trajectory.dcd",
    frame_range=(1, 100),
    interpolation='LINEAR'
)
```

## Material and Rendering

### mn.apply_cpk_colors
Apply CPK (Corey-Pauling-Koltun) color scheme.

Example:
```python
import bpy
# Apply standard CPK colors to all atoms
bpy.ops.mn.apply_cpk_colors()
```

### mn.create_bond_materials
Create materials for different bond types.

Parameters:
- material_style: str - Material style ('GLOSSY', 'MATTE', 'METALLIC')

Example:
```python
import bpy
# Create glossy bond materials
bpy.ops.mn.create_bond_materials(material_style='GLOSSY')
```

## Molecular Analysis Tools

### mn.measure_distance
Measure distance between atoms or groups.

Parameters:
- atom1: Object - First atom
- atom2: Object - Second atom
- display_label: bool, default True - Show distance label

Example:
```python
import bpy
# Measure distance between two selected atoms
distance = bpy.ops.mn.measure_distance(
    display_label=True
)
```

### mn.find_hydrogen_bonds
Identify hydrogen bonds in the structure.

Parameters:
- donor_acceptor_distance: float, default 3.5 - Maximum D-A distance
- angle_cutoff: float, default 120.0 - Minimum D-H-A angle

Example:
```python
import bpy
# Find hydrogen bonds
bpy.ops.mn.find_hydrogen_bonds(
    donor_acceptor_distance=3.2,
    angle_cutoff=130.0
)
```

## Best Practices for Molecular Visualization

### Performance Optimization
- Use level-of-detail (LOD) for large structures
- Optimize material complexity for real-time rendering
- Use instancing for repeated molecular units

### Visualization Guidelines
- Choose appropriate representation for the scientific question
- Use consistent color schemes across related structures
- Consider the target audience when selecting detail level

### Common Workflows
1. Import structure from PDB/SDF
2. Clean and prepare the structure
3. Apply appropriate visualization style
4. Add materials and lighting
5. Set up camera and render settings
6. Export or render final visualization

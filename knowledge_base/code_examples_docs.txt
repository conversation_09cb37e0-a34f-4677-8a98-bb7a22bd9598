# Blender Python Code Examples Library - Enhanced Version
# Version: 2.0.0 - Expanded for Task 4.4
# Last Updated: 2025-07-19

## Complete Scene Creation Examples

### Basic Scene Setup
```python
import bpy
import bmesh
from mathutils import Vector

def create_basic_scene():
    """Create a basic scene with objects, lighting, and camera."""
    # Clear existing mesh objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False, confirm=False)
    
    # Create ground plane
    bpy.ops.mesh.primitive_plane_add(size=10, location=(0, 0, 0))
    ground = bpy.context.active_object
    ground.name = "Ground"
    
    # Create main objects
    bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 1))
    cube = bpy.context.active_object
    cube.name = "MainCube"
    
    bpy.ops.mesh.primitive_uv_sphere_add(radius=1, location=(3, 0, 1))
    sphere = bpy.context.active_object
    sphere.name = "MainSphere"
    
    bpy.ops.mesh.primitive_cylinder_add(radius=0.8, depth=2, location=(-3, 0, 1))
    cylinder = bpy.context.active_object
    cylinder.name = "MainCylinder"
    
    # Add lighting
    bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
    sun = bpy.context.active_object
    sun.name = "MainSun"
    sun.data.energy = 3.0
    
    # Add camera
    bpy.ops.object.camera_add(location=(7, -7, 5))
    camera = bpy.context.active_object
    camera.name = "MainCamera"
    
    # Point camera at origin
    camera.rotation_euler = (1.1, 0, 0.785)
    
    return {
        'ground': ground,
        'cube': cube,
        'sphere': sphere,
        'cylinder': cylinder,
        'sun': sun,
        'camera': camera
    }
```

### Advanced Material Creation
```python
import bpy

def create_pbr_material(name, base_color, metallic=0.0, roughness=0.5):
    """Create a PBR material with specified properties."""
    # Create new material
    mat = bpy.data.materials.new(name=name)
    mat.use_nodes = True
    
    # Clear default nodes
    mat.node_tree.nodes.clear()
    
    # Add Principled BSDF
    principled = mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
    principled.location = (0, 0)
    
    # Add Material Output
    output = mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
    output.location = (300, 0)
    
    # Connect nodes
    mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Set material properties
    principled.inputs['Base Color'].default_value = (*base_color, 1.0)
    principled.inputs['Metallic'].default_value = metallic
    principled.inputs['Roughness'].default_value = roughness
    
    return mat

def create_glass_material(name, color, transmission=1.0, ior=1.45):
    """Create a glass material."""
    mat = bpy.data.materials.new(name=name)
    mat.use_nodes = True
    mat.node_tree.nodes.clear()
    
    # Add nodes
    principled = mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
    output = mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
    
    # Position nodes
    principled.location = (0, 0)
    output.location = (300, 0)
    
    # Connect
    mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Set glass properties
    principled.inputs['Base Color'].default_value = (*color, 1.0)
    principled.inputs['Transmission'].default_value = transmission
    principled.inputs['IOR'].default_value = ior
    principled.inputs['Roughness'].default_value = 0.0
    
    return mat

def apply_materials_to_scene():
    """Apply different materials to objects in the scene."""
    # Create materials
    red_metal = create_pbr_material("RedMetal", (0.8, 0.2, 0.2), metallic=1.0, roughness=0.2)
    blue_plastic = create_pbr_material("BluePlastic", (0.2, 0.2, 0.8), metallic=0.0, roughness=0.7)
    clear_glass = create_glass_material("ClearGlass", (1.0, 1.0, 1.0), transmission=1.0)
    
    # Apply to objects
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH':
            if 'Cube' in obj.name:
                obj.data.materials.append(red_metal)
            elif 'Sphere' in obj.name:
                obj.data.materials.append(clear_glass)
            elif 'Cylinder' in obj.name:
                obj.data.materials.append(blue_plastic)
```

### Complex Geometry Creation
```python
import bpy
import bmesh
from mathutils import Vector
import math

def create_parametric_surface(u_res=32, v_res=32):
    """Create a parametric surface using bmesh."""
    # Create new mesh
    mesh = bpy.data.meshes.new("ParametricSurface")
    bm = bmesh.new()
    
    # Generate vertices
    for i in range(u_res):
        for j in range(v_res):
            u = i / (u_res - 1) * 2 * math.pi
            v = j / (v_res - 1) * math.pi
            
            # Parametric equations for a torus
            R = 3.0  # Major radius
            r = 1.0  # Minor radius
            
            x = (R + r * math.cos(v)) * math.cos(u)
            y = (R + r * math.cos(v)) * math.sin(u)
            z = r * math.sin(v)
            
            bm.verts.new((x, y, z))
    
    # Ensure face index is valid
    bm.verts.ensure_lookup_table()
    
    # Create faces
    for i in range(u_res - 1):
        for j in range(v_res - 1):
            v1 = i * v_res + j
            v2 = i * v_res + (j + 1)
            v3 = (i + 1) * v_res + (j + 1)
            v4 = (i + 1) * v_res + j
            
            bm.faces.new([bm.verts[v1], bm.verts[v2], bm.verts[v3], bm.verts[v4]])
    
    # Update mesh
    bm.to_mesh(mesh)
    bm.free()
    
    # Create object
    obj = bpy.data.objects.new("ParametricSurface", mesh)
    bpy.context.collection.objects.link(obj)
    
    return obj

def create_fractal_tree(iterations=4, branch_length=2.0, angle=30):
    """Create a fractal tree structure."""
    def add_branch(start_pos, direction, length, iteration):
        if iteration <= 0:
            return
        
        # Calculate end position
        end_pos = start_pos + direction * length
        
        # Create branch (cylinder)
        bpy.ops.mesh.primitive_cylinder_add(
            radius=length * 0.1,
            depth=length,
            location=(start_pos + end_pos) / 2
        )
        
        branch = bpy.context.active_object
        branch.name = f"Branch_{iteration}"
        
        # Orient branch
        branch.rotation_euler = direction.to_track_quat('Z', 'Y').to_euler()
        
        # Create child branches
        if iteration > 1:
            # Left branch
            left_dir = direction.copy()
            left_dir.rotate(Vector((0, 0, 1)) * math.radians(angle))
            add_branch(end_pos, left_dir, length * 0.7, iteration - 1)
            
            # Right branch
            right_dir = direction.copy()
            right_dir.rotate(Vector((0, 0, 1)) * math.radians(-angle))
            add_branch(end_pos, right_dir, length * 0.7, iteration - 1)
    
    # Start the tree
    start_position = Vector((0, 0, 0))
    initial_direction = Vector((0, 0, 1))
    add_branch(start_position, initial_direction, branch_length, iterations)
```

### Animation Examples
```python
import bpy
from mathutils import Vector
import math

def create_orbital_animation(obj, center=Vector((0, 0, 0)), radius=5.0, duration=120):
    """Create orbital animation for an object."""
    # Clear existing keyframes
    obj.animation_data_clear()
    
    # Set up animation
    for frame in range(1, duration + 1):
        bpy.context.scene.frame_set(frame)
        
        # Calculate position
        angle = (frame - 1) / duration * 2 * math.pi
        x = center.x + radius * math.cos(angle)
        y = center.y + radius * math.sin(angle)
        z = center.z
        
        obj.location = (x, y, z)
        obj.keyframe_insert(data_path="location", frame=frame)
        
        # Add rotation
        obj.rotation_euler.z = angle
        obj.keyframe_insert(data_path="rotation_euler", frame=frame)

def create_wave_animation(obj, amplitude=2.0, frequency=1.0, duration=120):
    """Create wave motion animation."""
    original_z = obj.location.z
    
    for frame in range(1, duration + 1):
        bpy.context.scene.frame_set(frame)
        
        # Calculate wave position
        time = (frame - 1) / 24.0  # Convert to seconds (24 fps)
        z_offset = amplitude * math.sin(2 * math.pi * frequency * time)
        
        obj.location.z = original_z + z_offset
        obj.keyframe_insert(data_path="location", frame=frame)

def setup_camera_tracking(camera, target):
    """Set up camera to track a target object."""
    # Add Track To constraint
    constraint = camera.constraints.new(type='TRACK_TO')
    constraint.target = target
    constraint.track_axis = 'TRACK_NEGATIVE_Z'
    constraint.up_axis = 'UP_Y'
```

### Modifier Examples
```python
import bpy

def add_array_modifier(obj, count=5, offset=(2, 0, 0)):
    """Add array modifier to object."""
    modifier = obj.modifiers.new(name="Array", type='ARRAY')
    modifier.count = count
    modifier.relative_offset_displace = offset
    return modifier

def add_wave_modifier(obj, height=0.5, width=1.0, speed=1.0):
    """Add wave modifier to object."""
    modifier = obj.modifiers.new(name="Wave", type='WAVE')
    modifier.height = height
    modifier.width = width
    modifier.speed = speed
    modifier.use_z = False
    return modifier

def add_solidify_modifier(obj, thickness=0.1):
    """Add solidify modifier to object."""
    modifier = obj.modifiers.new(name="Solidify", type='SOLIDIFY')
    modifier.thickness = thickness
    return modifier

def create_modified_objects():
    """Create objects with various modifiers."""
    # Create base objects
    bpy.ops.mesh.primitive_plane_add(size=4, location=(0, 0, 0))
    plane = bpy.context.active_object
    plane.name = "WavePlane"
    
    bpy.ops.mesh.primitive_cube_add(size=1, location=(5, 0, 0))
    cube = bpy.context.active_object
    cube.name = "ArrayCube"
    
    # Add modifiers
    add_wave_modifier(plane, height=0.5, width=1.5, speed=2.0)
    add_solidify_modifier(plane, thickness=0.05)
    
    add_array_modifier(cube, count=7, offset=(1.5, 0, 0))
    add_solidify_modifier(cube, thickness=0.1)
```

### Utility Functions
```python
import bpy
from mathutils import Vector

def select_objects_by_name_pattern(pattern):
    """Select objects whose names contain the pattern."""
    bpy.ops.object.select_all(action='DESELECT')
    selected = []
    
    for obj in bpy.context.scene.objects:
        if pattern.lower() in obj.name.lower():
            obj.select_set(True)
            selected.append(obj)
    
    return selected

def batch_rename_objects(old_pattern, new_pattern):
    """Batch rename objects."""
    for obj in bpy.context.scene.objects:
        if old_pattern in obj.name:
            obj.name = obj.name.replace(old_pattern, new_pattern)

def get_object_bounds(obj):
    """Get object bounding box in world coordinates."""
    bbox_corners = [obj.matrix_world @ Vector(corner) for corner in obj.bound_box]
    
    min_x = min(corner.x for corner in bbox_corners)
    max_x = max(corner.x for corner in bbox_corners)
    min_y = min(corner.y for corner in bbox_corners)
    max_y = max(corner.y for corner in bbox_corners)
    min_z = min(corner.z for corner in bbox_corners)
    max_z = max(corner.z for corner in bbox_corners)
    
    return {
        'min': Vector((min_x, min_y, min_z)),
        'max': Vector((max_x, max_y, max_z)),
        'center': Vector(((min_x + max_x) / 2, (min_y + max_y) / 2, (min_z + max_z) / 2)),
        'size': Vector((max_x - min_x, max_y - min_y, max_z - min_z))
    }

def align_objects_to_grid(objects, spacing=2.0):
    """Align objects to a grid layout."""
    grid_size = int(math.ceil(math.sqrt(len(objects))))
    
    for i, obj in enumerate(objects):
        row = i // grid_size
        col = i % grid_size
        
        x = col * spacing
        y = row * spacing
        z = 0
        
        obj.location = (x, y, z)

def export_scene_info():
    """Export scene information to console."""
    print("=== SCENE INFORMATION ===")
    print(f"Total objects: {len(bpy.context.scene.objects)}")
    
    object_types = {}
    for obj in bpy.context.scene.objects:
        obj_type = obj.type
        object_types[obj_type] = object_types.get(obj_type, 0) + 1
    
    for obj_type, count in object_types.items():
        print(f"{obj_type}: {count}")
    
    print(f"Total materials: {len(bpy.data.materials)}")
    print(f"Total textures: {len(bpy.data.textures)}")
    print(f"Render engine: {bpy.context.scene.render.engine}")
```

### Complete Example: Molecular Scene
```python
import bpy
import math
from mathutils import Vector

def create_molecular_scene():
    """Create a complete molecular visualization scene."""
    # Clear scene
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False, confirm=False)
    
    # Create water molecule
    # Oxygen atom (red)
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.8, location=(0, 0, 0))
    oxygen = bpy.context.active_object
    oxygen.name = "Oxygen"
    
    # Hydrogen atoms (white)
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.4, location=(1.2, 0.8, 0))
    hydrogen1 = bpy.context.active_object
    hydrogen1.name = "Hydrogen1"
    
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.4, location=(1.2, -0.8, 0))
    hydrogen2 = bpy.context.active_object
    hydrogen2.name = "Hydrogen2"
    
    # Create bonds (cylinders)
    bond_radius = 0.1
    
    # O-H bond 1
    bond1_pos = (oxygen.location + hydrogen1.location) / 2
    bpy.ops.mesh.primitive_cylinder_add(radius=bond_radius, depth=1.0, location=bond1_pos)
    bond1 = bpy.context.active_object
    bond1.name = "Bond_OH1"
    
    # O-H bond 2
    bond2_pos = (oxygen.location + hydrogen2.location) / 2
    bpy.ops.mesh.primitive_cylinder_add(radius=bond_radius, depth=1.0, location=bond2_pos)
    bond2 = bpy.context.active_object
    bond2.name = "Bond_OH2"
    
    # Create materials
    red_mat = create_pbr_material("OxygenMaterial", (1.0, 0.2, 0.2))
    white_mat = create_pbr_material("HydrogenMaterial", (1.0, 1.0, 1.0))
    gray_mat = create_pbr_material("BondMaterial", (0.5, 0.5, 0.5))
    
    # Apply materials
    oxygen.data.materials.append(red_mat)
    hydrogen1.data.materials.append(white_mat)
    hydrogen2.data.materials.append(white_mat)
    bond1.data.materials.append(gray_mat)
    bond2.data.materials.append(gray_mat)
    
    # Set up lighting
    bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
    sun = bpy.context.active_object
    sun.data.energy = 3.0
    
    # Set up camera
    bpy.ops.object.camera_add(location=(5, -5, 3))
    camera = bpy.context.active_object
    setup_camera_tracking(camera, oxygen)
    
    # Group objects
    molecule_collection = bpy.data.collections.new("WaterMolecule")
    bpy.context.scene.collection.children.link(molecule_collection)
    
    for obj in [oxygen, hydrogen1, hydrogen2, bond1, bond2]:
        bpy.context.scene.collection.objects.unlink(obj)
        molecule_collection.objects.link(obj)
    
    return {
        'oxygen': oxygen,
        'hydrogens': [hydrogen1, hydrogen2],
        'bonds': [bond1, bond2],
        'collection': molecule_collection
    }
```

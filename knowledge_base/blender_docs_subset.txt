# Blender Python API Documentation Subset - Enhanced Version
# Version: 2.0.0 - Expanded for Task 4.4
# Last Updated: 2025-07-19

## Basic Object Creation

### bpy.ops.mesh.primitive_cube_add
Creates a cube mesh primitive in the current scene.

Parameters:
- size: float, default 2.0 - Size of the cube
- location: Vector, default (0, 0, 0) - Location for the new cube
- rotation: Vector, default (0, 0, 0) - Rotation for the new cube
- scale: Vector, default (1, 1, 1) - Scale for the new cube
- enter_editmode: bool, default False - Enter edit mode after creation
- align: str, default 'WORLD' - Alignment mode ('WORLD', 'VIEW', 'CURSOR')

Example:
```python
import bpy
# Basic cube creation
bpy.ops.mesh.primitive_cube_add(size=2.0, location=(0, 0, 0))

# Advanced cube with custom properties
bpy.ops.mesh.primitive_cube_add(
    size=1.5,
    location=(2, 0, 1),
    rotation=(0.5, 0.3, 0),
    scale=(1, 2, 0.5),
    enter_editmode=False,
    align='WORLD'
)
```

### bpy.ops.mesh.primitive_plane_add
Creates a plane mesh primitive in the current scene.

Parameters:
- size: float, default 2.0 - Size of the plane
- location: Vector, default (0, 0, 0) - Location for the new plane
- rotation: Vector, default (0, 0, 0) - Rotation for the new plane

Example:
```python
import bpy
bpy.ops.mesh.primitive_plane_add(size=4.0, location=(0, 0, 0))
```

### bpy.ops.mesh.primitive_uv_sphere_add
Creates a UV sphere mesh primitive in the current scene.

Parameters:
- radius: float, default 1.0 - Radius of the sphere
- location: Vector, default (0, 0, 0) - Location for the new sphere
- rotation: Vector, default (0, 0, 0) - Rotation for the new sphere
- subdivisions: int, default 32 - Number of subdivisions

Example:
```python
import bpy
bpy.ops.mesh.primitive_uv_sphere_add(radius=1.0, location=(2, 0, 0))
```

### bpy.ops.mesh.primitive_cylinder_add
Creates a cylinder mesh primitive in the current scene.

Parameters:
- radius: float, default 1.0 - Radius of the cylinder
- depth: float, default 2.0 - Depth (height) of the cylinder
- location: Vector, default (0, 0, 0) - Location for the new cylinder
- rotation: Vector, default (0, 0, 0) - Rotation for the new cylinder

Example:
```python
import bpy
bpy.ops.mesh.primitive_cylinder_add(radius=1.0, depth=2.0, location=(0, 2, 0))
```

## Object Manipulation

### bpy.context.object
Reference to the currently active object in the scene.

Properties:
- location: Vector - Object's location in 3D space
- rotation_euler: Vector - Object's rotation in Euler angles
- scale: Vector - Object's scale factors
- name: str - Object's name

Example:
```python
import bpy
obj = bpy.context.object
obj.location = (1, 2, 3)
obj.rotation_euler = (0, 0, 1.57)  # 90 degrees in radians
obj.scale = (2, 2, 2)
```

### bpy.data.objects
Collection of all objects in the current Blender file.

Methods:
- new(name, object_data): Create a new object
- remove(object): Remove an object

Example:
```python
import bpy
# Get object by name
cube = bpy.data.objects.get("Cube")
if cube:
    bpy.data.objects.remove(cube, do_unlink=True)
```

## Material System

### bpy.data.materials.new
Creates a new material.

Parameters:
- name: str - Name of the material

Example:
```python
import bpy
material = bpy.data.materials.new(name="MyMaterial")
material.diffuse_color = (1.0, 0.0, 0.0, 1.0)  # Red color
```

### Object Material Assignment
Assign materials to objects.

Example:
```python
import bpy
obj = bpy.context.object
material = bpy.data.materials.new(name="BlueMaterial")
material.diffuse_color = (0.0, 0.0, 1.0, 1.0)  # Blue color

# Assign material to object
if obj.data.materials:
    obj.data.materials[0] = material
else:
    obj.data.materials.append(material)
```

## Scene Management

### bpy.context.scene
Reference to the current scene.

Properties:
- objects: Collection of objects in the scene
- collection: Main collection of the scene

Example:
```python
import bpy
scene = bpy.context.scene
print(f"Scene has {len(scene.objects)} objects")
```

### bpy.ops.object.select_all
Select or deselect all objects in the scene.

Parameters:
- action: str - 'SELECT', 'DESELECT', or 'INVERT'

Example:
```python
import bpy
bpy.ops.object.select_all(action='DESELECT')
```

## File Operations

### bpy.ops.wm.save_as_mainfile
Save the current Blender file.

Parameters:
- filepath: str - Path where to save the file

Example:
```python
import bpy
bpy.ops.wm.save_as_mainfile(filepath="/path/to/file.blend")
```

### bpy.ops.export_scene.obj
Export scene as OBJ file.

Parameters:
- filepath: str - Path where to save the OBJ file
- use_selection: bool - Export only selected objects

Example:
```python
import bpy
bpy.ops.export_scene.obj(filepath="/path/to/model.obj", use_selection=False)
```

## Molecular Nodes (MCP) Basics

### Molecular Nodes Overview
Molecular Nodes is a Blender add-on for creating and manipulating molecular structures.

Key Concepts:
- Atoms: Individual atomic elements
- Bonds: Connections between atoms
- Molecules: Collections of atoms and bonds
- Protein structures: Complex molecular assemblies

### Basic Atom Creation
Create individual atoms using Molecular Nodes.

Example:
```python
import bpy
# Note: This requires Molecular Nodes add-on to be installed and enabled
# Basic atom creation workflow
bpy.ops.mn.add_atom(element='C', location=(0, 0, 0))  # Carbon atom
bpy.ops.mn.add_atom(element='O', location=(1.5, 0, 0))  # Oxygen atom
```

### Molecular Structure Import
Import molecular structures from common file formats.

Supported formats:
- PDB (Protein Data Bank)
- SDF (Structure Data File)
- MOL (Molecule file)

Example:
```python
import bpy
# Import PDB file
bpy.ops.mn.import_pdb(filepath="/path/to/structure.pdb")
```

### Bond Creation
Create bonds between atoms in molecular structures.

Example:
```python
import bpy
# Create bond between two selected atoms
bpy.ops.mn.create_bond(bond_type='SINGLE')
```

## Common Patterns and Best Practices

### Clear Default Scene
Remove default objects to start with clean scene.

```python
import bpy

# Delete default cube, light, and camera
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Or selectively delete default cube
if "Cube" in bpy.data.objects:
    bpy.data.objects.remove(bpy.data.objects["Cube"], do_unlink=True)
```

### Error Handling
Proper error handling in Blender scripts.

```python
import bpy

try:
    bpy.ops.mesh.primitive_cube_add()
    print("Cube created successfully")
except Exception as e:
    print(f"Error creating cube: {e}")
```

### Object Naming and Organization
Best practices for naming and organizing objects.

```python
import bpy

# Create and name objects systematically
bpy.ops.mesh.primitive_cube_add()
cube = bpy.context.object
cube.name = "MainCube"

# Group related objects in collections
collection = bpy.data.collections.new("Molecules")
bpy.context.scene.collection.children.link(collection)
collection.objects.link(cube)
```

### Performance Considerations
Tips for efficient Blender scripting.

- Use bpy.context.evaluated_depsgraph_get() for mesh data access
- Batch operations when possible
- Avoid unnecessary scene updates during batch processing
- Use bmesh for complex mesh operations

```python
import bpy
import bmesh

# Efficient mesh creation using bmesh
bm = bmesh.new()
bmesh.ops.create_cube(bm, size=2.0)

mesh = bpy.data.meshes.new("EfficientCube")
bm.to_mesh(mesh)
bm.free()

obj = bpy.data.objects.new("EfficientCube", mesh)
bpy.context.collection.objects.link(obj)
```

## Advanced Mesh Operations

### bpy.ops.mesh.subdivide
Subdivide selected edges and faces.

Parameters:
- number_cuts: int, default 1 - Number of cuts to make
- smoothness: float, default 0.0 - Smoothness factor
- quadcorner: str, default 'STRAIGHT_CUT' - Quad corner type

Example:
```python
import bpy
# Enter edit mode and select all
bpy.ops.object.mode_set(mode='EDIT')
bpy.ops.mesh.select_all(action='SELECT')
bpy.ops.mesh.subdivide(number_cuts=2, smoothness=0.5)
bpy.ops.object.mode_set(mode='OBJECT')
```

### bpy.ops.mesh.extrude_region_move
Extrude selected region and move.

Parameters:
- TRANSFORM_OT_translate: dict - Translation parameters

Example:
```python
import bpy
bpy.ops.object.mode_set(mode='EDIT')
bpy.ops.mesh.select_all(action='SELECT')
bpy.ops.mesh.extrude_region_move(
    TRANSFORM_OT_translate={"value": (0, 0, 2)}
)
bpy.ops.object.mode_set(mode='OBJECT')
```

### bpy.ops.mesh.bevel
Bevel selected edges or vertices.

Parameters:
- offset: float, default 0.0 - Bevel offset
- segments: int, default 1 - Number of segments
- profile: float, default 0.5 - Profile shape

Example:
```python
import bpy
bpy.ops.object.mode_set(mode='EDIT')
bpy.ops.mesh.select_all(action='SELECT')
bpy.ops.mesh.bevel(offset=0.1, segments=3, profile=0.5)
bpy.ops.object.mode_set(mode='OBJECT')
```

## Modifier Operations

### bpy.ops.object.modifier_add
Add a modifier to the active object.

Parameters:
- type: str - Modifier type (e.g., 'ARRAY', 'MIRROR', 'SOLIDIFY')

Example:
```python
import bpy
# Add array modifier
bpy.ops.object.modifier_add(type='ARRAY')
array_mod = bpy.context.object.modifiers[-1]
array_mod.count = 5
array_mod.relative_offset_displace[0] = 2.0

# Add mirror modifier
bpy.ops.object.modifier_add(type='MIRROR')
mirror_mod = bpy.context.object.modifiers[-1]
mirror_mod.use_axis[0] = True
mirror_mod.use_axis[1] = False
```

### bpy.ops.object.modifier_apply
Apply a modifier to the object.

Parameters:
- modifier: str - Name of the modifier to apply

Example:
```python
import bpy
obj = bpy.context.object
if obj.modifiers:
    modifier_name = obj.modifiers[0].name
    bpy.ops.object.modifier_apply(modifier=modifier_name)
```

## Animation and Keyframes

### bpy.ops.anim.keyframe_insert_menu
Insert keyframes for selected properties.

Parameters:
- type: str - Keyframe type ('Location', 'Rotation', 'Scale', 'LocRot', 'LocScale', 'RotScale', 'LocRotScale')

Example:
```python
import bpy
# Set object location and insert keyframe
obj = bpy.context.object
obj.location = (0, 0, 0)
bpy.context.scene.frame_set(1)
bpy.ops.anim.keyframe_insert_menu(type='Location')

# Move to frame 50 and set new location
bpy.context.scene.frame_set(50)
obj.location = (5, 0, 0)
bpy.ops.anim.keyframe_insert_menu(type='Location')
```

### bpy.ops.anim.keyframe_delete
Delete keyframes for selected properties.

Example:
```python
import bpy
bpy.ops.anim.keyframe_delete(type='Location')
```

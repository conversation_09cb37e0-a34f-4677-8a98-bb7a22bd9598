# 基于图像与规格的Blender 3D模型生成AI Agent - 开发计划优化版

本开发计划将严格遵循循序渐进、可量化和测试优先的原则。每个任务都将伴随明确的交付物和验收标准，并强调单元测试、集成测试的先行。

## 项目阶段和开发任务 (优化版)

### 阶段1：基础设施与Agent框架确立 (预计：2-3周)

*   **目标**：搭建基础开发环境，选定Agent框架，并建立最小可用的Blender执行链路。

*   **任务 1.1: 开发环境配置与Blender集成验证**
    *   **描述**: 安装Python依赖，配置Blender路径，并编写一个简单的Python脚本在Blender中执行，验证Blender无头模式运行及`bpy`模块可用。**特别注意Blender版本兼容性和环境隔离（如使用conda/venv）。**
    *   **交付物**:
        *   `docs/setup_guide.md`：详细的开发环境设置指南。
        *   `blender_interface/test_blender_env.py`：一个能成功在Blender无头模式下创建一个立方体并保存为`.blend`文件的Python脚本。
        *   `environment.yml` 或 `requirements.txt`：清晰定义的项目依赖。
    *   **量化标准**:
        *   Blender无头模式启动成功率：100%。
        *   测试脚本执行成功率：100%。
        *   生成的`.blend`文件可在Blender中正常打开。
        *   环境配置脚本（或文档）可由新成员独立执行并成功搭建。
    *   **测试策略**: 编写单元测试验证`subprocess`调用Blender的正确性；编写集成测试验证`test_blender_env.py`脚本在真实Blender环境中的执行结果。自动化环境搭建验证脚本。

*   **任务 1.2: Agent框架（AutoGen + Ray RLlib）初步集成与核心Agent通信模式定义**
    *   **描述**: 初始化AutoGen对话管理器，并集成Ray RLlib进行强化学习驱动的Agent决策，验证Agent间的基本消息传递和策略优化机制。
    *   **详细步骤**：
        1.  **理解 AutoGen 基础知识：**
            *   熟悉 AutoGen 的核心概念：`UserProxyAgent`、`AssistantAgent`、`GroupChat`、`GroupChatManager`。
        2.  **理解 Ray RLlib 集成：**
            *   研究 Ray RLlib 的核心概念：Environment、Policy、Trainer。
            *   设计Agent决策的状态空间和动作空间。
            *   定义奖励函数来指导Agent学习。
        3.  **创建RL环境：**
            *   实现一个简单的Gym环境，模拟Agent的工具选择场景。
            *   定义状态（当前任务上下文）、动作（可选工具）、奖励（执行结果）。
        4.  **集成RLlib策略：**
            *   使用PPO算法训练一个简单的策略。
            *   将训练好的策略集成到AssistantAgent中。
        5.  **设计初步的 Agent 通信协议 (V1)：**
            *   考虑 Agent 之间将交换的消息类型（例如，图像分析结果、规格请求、代码生成请求、执行结果、错误消息）。
            *   定义这些消息的基本结构。JSON 是一个很好的选择，因为它具有灵活性。
            *   需要考虑的关键字段：`sender_id`（发送者 ID）、`receiver_id`（接收者 ID）、`message_type`（消息类型，例如 `image_analysis_result`、`spec_request`）、`payload`（实际数据）、`timestamp`（时间戳）。
            *   将此协议文档化在 `docs/agent_communication_protocol_v1.md` 中。这将是一个动态文档，随着项目发展而演进。
            *   **通信消息示例 (Minimal JSON Example):**
                ```protocol_example.json
                {
                  "message_id": "uuid-1234",
                  "sender_id": "ImageAnalysisAgent",
                  "receiver_id": "SpecGenerationAgent",
                  "message_type": "image_analysis_result",
                  "timestamp": "2024-05-21T10:00:00Z",
                  "payload": {
                    "source_image_path": "/path/to/image.png",
                    "detected_objects": [
                      {"label": "cube", "bbox": [10, 10, 50, 50]},
                      {"label": "sphere", "bbox": [60, 60, 100, 100]}
                    ]
                  },
                  "metadata": {
                    "confidence_score": 0.95
                  }
                }
                ```
                上述示例展示了一个从图像分析Agent发送给规格生成Agent的消息，包含了识别到的对象和主要意图。
        6.  **实现 `agents/autogen_om_poc.py`：**
            *   编写 Python 脚本，将步骤 3、4 和 5 结合起来。
            *   配置 AutoGen `GroupChatManager` 或直接的 Agent 到 Agent 的通信。
            *   确保 Assistant Agent 成功调用由 OpenMenus-RL 驱动的虚拟工具。
            *   根据量化标准，验证至少 3 轮对话的消息传递成功。
        7.  **开发测试策略并实现基本测试：**
            *   **OpenMenus-RL 的单元测试：** 测试工具注册、选择逻辑和模拟工具执行。
            *   **AutoGen 对话的集成测试：** 模拟 `UserProxyAgent` 和 `AssistantAgent` 之间的对话，断言消息传递是否正确以及虚拟工具是否被调用。
            *   **协议符合性测试：** 验证交换的消息是否符合 `agent_communication_protocol_v1.md` 中定义的结构。
    *   **交付物**:
        *   `agents/autogen_om_poc.py`：包含两个Agent（User Proxy和Assistant Agent）的最小化AutoGen配置，Assistant Agent通过OpenMenus-RL选择并执行一个虚拟工具（例如，打印“Hello, World!”）。
        *   `docs/agent_communication_protocol_v1.md`：初步的Agent通信协议设计文档。
    *   **量化标准**:
        *   两个Agent间成功完成3轮以上对话，且OpenMenus-RL工具调用成功率100%。
        *   通信协议文档清晰，能指导后续Agent开发。
    *   **测试策略**: 编写单元测试验证OpenMenus-RL的工具注册和选择机制；编写集成测试验证AutoGen Agent间的对话流和虚拟工具执行，并验证消息格式符合协议定义。

*   **任务 1.3: 3D模型基本规格Schema定义与版本控制**
    *   **描述**: 设计一个初步的JSON Schema，能够描述基本几何体（立方体、球体、圆柱体）及其基本属性（位置、旋转、缩放、颜色）。**建立Schema的版本控制机制。**
    *   **交付物**: `models/specs/v1/base_model_spec.json`：定义基本3D模型属性的JSON Schema文件。
    *   **量化标准**:
        *   Schema符合JSON Schema Draft 7标准。
        *   能够通过`Pydantic`成功验证至少5个不同基本几何体的JSON规格示例。
        *   Schema版本管理流程明确。
    *   **测试策略**: 编写单元测试验证`Pydantic`模型对Schema的解析和数据验证能力；验证Schema版本更新的兼容性。

### 阶段2：核心Agent原型开发与端到端链路 (预计：4-5周)

*   **目标**：开发出系统核心模块和Agent的第一个版本，实现从图像到简单3D模型的端到端流程，并确保基础链路的稳定性。

*   **任务 2.1: 图像输入与预处理模块开发与健壮性提升** (`input_module/image_handler.py`)
    *   **描述**: 实现本地图像加载和基本的图像标准化（如缩放、裁剪）。集成一个AI图像生成API（如DALL-E），支持通过文本提示生成图像。**增加输入校验和错误处理机制（如文件不存在、格式不支持、API调用失败重试）。**
    *   **交付物**:
        *   `input_module/image_handler.py`：核心图像处理类。
        *   `tests/test_image_handler.py`：包含本地图像加载、尺寸标准化和API调用（Mocked）的单元测试，**以及异常处理测试**。
    *   **量化标准**:
        *   支持PNG, JPG, BMP格式，本地图像加载成功率：100%。
        *   图像标准化（如256x256像素）输出准确率：100%。
        *   DALL-E API调用成功率：>95% (外部API稳定性考量)；**API失败时的优雅降级或错误报告机制**。
    *   **测试策略**: 测试本地文件读取、图像处理功能和异常路径；Mocking外部API调用以验证接口集成和错误处理逻辑。

*   **任务 2.2: 知识Agent原型开发与检索评估** (`agents/knowledge_agent.py`)
    *   **描述**: 建立一个基于向量数据库的知识检索原型。加载少量Blender Python API（`bpy`）和MCP基础文档片段，实现通过语义搜索获取相关文本。**设计初步的知识检索效果评估指标。**
    *   **交付物**:
        *   `agents/knowledge_agent.py`：知识Agent核心逻辑。
        *   `knowledge_base/blender_docs_subset.txt`：初步的Blender和MCP文档片段。
        *   `tests/test_knowledge_agent.py`：包含知识库加载、文本嵌入、向量存储和语义检索的单元测试。
        *   `docs/knowledge_retrieval_metrics.md`：知识检索评估指标文档。
    *   **量化标准**:
        *   知识库文本加载和嵌入成功率：100%。
        *   对于预定义的基础API查询，知识检索结果的准确率：>80%。**Top-K检索的平均相关性评分**。
    *   **测试策略**: 针对RAG流程的各个步骤编写单元测试，包括嵌入生成、向量检索的正确性。

*   **任务 2.3: Blender执行模块开发（基础版）与输出解析** (`blender_interface/blender_executor.py`)
    *   **描述**: 实现通过Python `subprocess`模块启动Blender并执行指定的`.py`脚本，捕获Blender的stdout/stderr。**增加对Blender输出（如渲染路径、报错信息）的结构化解析功能。**
    *   **交付物**:
        *   `blender_interface/blender_executor.py`：Blender执行器的核心类。
        *   `tests/test_blender_executor.py`：单元测试，模拟Blender执行成功和失败的场景，验证输出捕获和解析。
    *   **量化标准**:
        *   给定有效Blender脚本，执行成功率：100%。
        *   给定包含错误的Blender脚本，能正确捕获`stderr`并解析关键错误信息：100%。
    *   **测试策略**: 编写单元测试以验证Blender进程的启动、脚本传递、输出捕获和错误信息解析机制。

*   **任务 2.4: 初始图像分析Agent开发（基础版）** (`agents/image_analysis_agent.py`)
    *   **描述**: 使用预训练的CV模型（如简单图像分类或对象检测），识别图像中是否存在“立方体”、“球体”等基本几何体。**明确模型的输入输出格式，并增加对识别结果置信度的考量。**
    *   **交付物**:
        *   `agents/image_analysis_agent.py`：图像分析Agent的初步实现。
        *   `tests/test_image_analysis_agent.py`：包含对已知图像（含简单形状）进行识别的单元测试。
    *   **量化标准**:
        *   对预定义测试集（5张包含单一基本形状的图片），形状识别准确率：>85%。
        *   识别结果的输出格式符合预期，包含置信度分数。
    *   **测试策略**: 使用小型、受控的图像数据集进行单元测试和集成测试，确保其能够准确识别基本形状并输出置信度。

*   **任务 2.5: 初始规格生成Agent开发与Schema验证强化** (`agents/spec_generation_agent.py`)
    *   **描述**: 根据图像分析Agent识别出的基本形状，结合`知识Agent`提供的上下文，生成符合`base_model_spec.json`的3D模型规格文件。**强调生成规格的Schema强制验证，并处理验证失败的情况。**
    *   **交付物**:
        *   `agents/spec_generation_agent.py`：规格生成Agent的初步实现。
        *   `tests/test_spec_generation_agent.py`：单元测试，验证输入图像分析结果后，生成的JSON规格的正确性与Schema符合性，**以及处理不符合Schema的错误场景**。
    *   **量化标准**:
        *   对识别出的基本形状（如“立方体”），生成正确JSON规格的成功率：100%。
        *   生成的JSON规格符合`base_model_spec.json` Schema：100%。
    *   **测试策略**: 针对不同输入语义（如“红色的球体”），测试生成规格的准确性和Schema符合性，并模拟LLM生成不合规规格的情况，测试错误处理。

*   **任务 2.6: 初始规格到代码Agent开发与静态代码分析** (`agents/code_generation_agent.py`)
    *   **描述**: 实现将`base_model_spec.json`中定义的简单几何体规格，转换为对应的Blender Python (`bpy`) 代码。利用`知识Agent`辅助API查找。**集成Python静态代码分析工具（如`ast`模块或`flake8`轻量级检查），验证代码语法。**
    *   **交付物**:
        *   `agents/code_generation_agent.py`：代码生成Agent的初步实现。
        *   `tests/test_code_generation_agent.py`：单元测试，验证生成的Python脚本在语法和逻辑上是否正确（不实际执行Blender）。
    *   **量化标准**:
        *   对于每种基本几何体，生成的Python代码语法正确率：100%。
        *   生成的代码能通过AST解析：100%。
        *   生成的代码逻辑与规格一致性：>90%。
    *   **测试策略**: 编写单元测试，输入JSON规格，验证输出的Python代码是否符合预期。结合静态代码分析工具进行语法检查和基本语义验证。

### 阶段3：反馈循环、强化学习与MLOps基础集成 (预计：5-6周)

*   **目标**：引入自动化的错误调试与视觉反馈机制，并初步集成强化学习优化Agent决策。建立MLOps基础流程。

*   **任务 3.1: Validator/Debugger Agent 开发与代码级内循环集成** (`agents/validator_debugger_agent.py`)
    *   **描述**: 开发`Validator/DebuggerAgent`，能够接收Blender执行失败的错误日志，利用LLM分析错误原因（如API不存在、参数错误），并生成代码修复建议。将此Agent与`BlenderExecutor`和`规格到代码Agent`连接，形成代码级内循环。**增强错误日志的结构化解析和错误类型分类，提高诊断精度。**
    *   **交付物**:
        *   `agents/validator_debugger_agent.py`：Debugger Agent的核心实现。
        *   `tests/test_debugger_agent.py`：单元测试，包含模拟Blender错误日志，验证Debugger Agent的诊断和修复建议能力，**尤其关注不同错误类型的处理**。
    *   **量化标准**:
        *   对预设的10种常见Blender Python错误，诊断准确率：>70%，**并能分类错误类型**。
        *   对诊断出的错误，生成有效修复建议的成功率：>60%。
        *   成功触发内循环并重新尝试执行：100%。
    *   **测试策略**: 模拟Blender报错日志作为输入，测试Debugger Agent能否给出合理的修复建议，并衡量修复后的成功率。

*   **任务 3.2: Visual Critic Agent 开发与视觉反馈外循环集成** (`agents/visual_critic_agent.py`)
    *   **描述**: 开发`Visual Critic Agent`。`BlenderExecutor`在成功生成模型后，自动渲染预览图。`Visual Critic Agent`利用多模态LLM比较渲染图与原始输入图像，评估视觉一致性，并生成高层设计修正建议（如“颜色不符”、“形状不够圆润”），将建议反馈给`规格生成Agent`，形成外循环。**明确视觉评估的维度（形状、颜色、材质、相对位置等）。**
    *   **交付物**:
        *   `agents/visual_critic_agent.py`：Visual Critic Agent的核心实现。
        *   `tests/test_visual_critic_agent.py`：单元测试，验证多模态LLM对渲染图和原始图像的比较及反馈能力，**针对不同视觉偏差场景进行测试**。
    *   **量化标准**:
        *   对预设的5组“原始图-渲染图”，视觉一致性评估与人工评估一致性：>75%。
        *   生成有效设计修正建议的成功率：>60%。
        *   **反馈建议的特异性和可操作性评分：>3/5。**
    *   **测试策略**: 提供多对原始图像和渲染图像，测试Visual Critic Agent的反馈质量，并进行A/B测试评估不同反馈策略的效果。

*   **任务 3.3: Ray RLlib 基础集成、奖励函数设计与RL环境构建**
    *   **描述**: 在`知识Agent`和`规格到代码Agent`中初步集成Ray RLlib，注册其可用的工具集。定义初步的奖励函数，用于衡量Agent的决策质量（例如，代码生成成功，Blender执行成功）。**构建最小化的RL训练环境，确保奖励信号的正确传递。**
    *   **交付物**:
        *   更新的`agents/knowledge_agent.py`和`agents/code_generation_agent.py`：包含Ray RLlib集成代码。
        *   `docs/rl_reward_design.md`：详细的奖励函数设计文档。
        *   `rl_env/minimal_blender_task_env.py`：简化的RL环境定义。
    *   **量化标准**:
        *   Ray RLlib工具选择机制在Agent中被成功调用：100%。
        *   初步奖励函数能够正确计算并报告奖励值。
        *   RL环境能够模拟Agent与Blender的交互，并正确返回状态和奖励。
    *   **测试策略**: 验证RL框架的集成是否影响现有功能；通过模拟奖励信号，测试奖励函数的正确性；运行RL环境的冒烟测试。

*   **任务 3.4: MLOps基础：模型版本管理与实验追踪**
    *   **描述**: 为所有AI模型（CV模型、LLM配置、RL策略）建立版本管理和实验追踪机制（如使用MLflow或DVC的轻量级集成）。
    *   **交付物**:
        *   `scripts/model_versioning.py`：模型版本管理示例脚本。
        *   `docs/mlops_guidelines.md`：MLOps基础实践指南。
    *   **量化标准**:
        *   每个训练好的模型都能被唯一标识和追踪。
        *   能记录模型的训练参数、指标和来源数据。
    *   **测试策略**: 运行示例模型训练，验证版本管理和实验追踪的有效性。

### 阶段4：功能增强、复杂性处理与RL策略优化 (预计：4-5周)

*   **目标**：提升图像分析、规格生成和代码生成的复杂性处理能力，特别是对MCP的支持，并优化RL策略。

*   **任务 4.1: 增强图像分析Agent：复杂形状与场景理解** (`agents/image_analysis_agent.py`)
    *   **描述**: 引入更先进的计算机视觉技术（如语义分割、深度估计），以识别更复杂的3D形状、纹理和相对位置。集成多模态LLM进行高层意图推断。**针对多物体、遮挡和光照变化进行优化。**
    *   **交付物**: 更新的`agents/image_analysis_agent.py`。
    *   **量化标准**:
        *   对包含复杂形状、多物体场景的图像，关键特征识别准确率：>70%。
        *   深度估计的平均相对误差（MRE）低于0.15。
        *   **多物体场景中相对位置识别准确率：>60%。**
    *   **测试策略**: 使用包含复杂场景的测试图片集，评估其识别能力和多模态理解能力，增加对抗性样本测试。

*   **任务 4.2: 改进规格生成Agent：复杂属性与结构扩展** (`agents/spec_generation_agent.py`)
    *   **描述**: 扩展3D模型规格Schema，支持更多复杂的属性（材质、灯光、高级修改器）和结构（如组合体、层次结构）。引入MCP规格模板。**确保新旧Schema版本的兼容性处理。**
    *   **交付物**:
        *   更新的`models/specs/v2/model_spec_schema.json`。
        *   更新的`agents/spec_generation_agent.py`。
    *   **量化标准**:
        *   Schema能够覆盖至少3种复杂材质类型、5种常见Blender修改器和2种MCP结构类型。
        *   针对复杂图像，生成的规格Schema符合性：100%。
        *   **新旧Schema转换成功率：100% (如果涉及转换)。**
    *   **测试策略**: 针对复杂输入，测试生成规格的准确性和Schema符合性，编写Schema迁移测试。

*   **任务 4.3: 增强规格到代码Agent：复杂功能与MCP支持** (`agents/code_generation_agent.py`)
    *   **描述**: 提升LLM生成Blender Python代码的能力，支持复杂材质、修改器、动画关键帧以及核心MCP功能（如创建分子、连接原子）。利用Debugger Agent反馈优化代码质量。**特别关注Blender Python API的正确用法和性能考量。**
    *   **交付物**:
        *   `agents/code_generation_agent.py`：代码生成Agent的初步实现。
        *   `tests/test_code_generation_agent.py`：单元测试，验证生成的Python脚本在语法和逻辑上是否正确（不实际执行Blender）。
    *   **量化标准**:
        *   生成的复杂模型Python脚本执行成功率：>85%。
        *   MCP相关代码生成准确性：>70%。
        *   Debugger Agent帮助修复代码的平均迭代次数：<3次。
        *   **生成的Blender文件加载速度和渲染性能符合预期。**
    *   **测试策略**: 编写针对复杂规格的集成测试，验证生成的代码在Blender中的实际效果，并记录Debugger Agent的介入情况，进行性能基准测试。

*   **任务 4.4: 知识Agent扩展与检索策略优化** (`agents/knowledge_agent.py`)
    *   **描述**: 大规模扩充知识库，特别强化MCP的文档和代码示例。优化知识检索策略，提高RAG的精度和召回率。**探索混合检索（关键词+向量）或Reranking技术。**
    *   **交付物**:
        *   更新的`agents/knowledge_agent.py`和扩充的`knowledge_base`目录。
    *   **量化标准**:
        *   知识库文档数量增加200%。
        *   关键MCP查询的知识检索准确率：>90%。
        *   RAG系统对LLM幻觉率的降低：>20%。
        *   **检索速度提升：>15%。**
    *   **测试策略**: 增加知识库相关的基准测试，评估检索性能和对LLM生成代码的辅助效果，进行A/B测试比较不同检索策略。

*   **任务 4.5: Ray RLlib 策略训练与持续优化**
    *   **描述**: 基于系统运行过程中收集的成功/失败轨迹，对Ray RLlib策略进行离线训练和迭代优化，提升Agent的工具选择和决策效率。**探索在线学习或持续集成/持续部署(CI/CD)的RL模型更新流程。**
    *   **交付物**: 训练好的RL模型权重文件，MLOps管道配置。
    *   **量化标准**:
        *   RL Agent决策效率提升（如平均决策时间缩短15%）。
        *   最终模型生成成功率提升：>5%。
        *   关键任务（如Blender API调用）的成功率提升：>10%。
        *   **RL模型更新流程自动化程度：>80%。**
    *   **测试策略**: 运行基准测试，比较RL优化前后的系统整体性能和Agent决策效率。测试RL模型自动部署和更新的流程。

### 阶段5：系统集成、全面测试与部署准备 (预计：2-3周)

*   **目标**：完成所有Agent的集成，进行全面的系统级测试，并准备部署。

*   **任务 5.1: 编排Agent完善与端到端流程集成** (`main_orchestrator.py`)
    *   **描述**: 完成编排Agent的开发，将所有模块和Agent连接起来，实现端到端的数据流和控制流。确保内外部反馈循环的顺畅运作。**实现可配置的Agent编排策略。**
    *   **交付物**:
        *   `main_orchestrator.py`：完整的编排逻辑。
    *   **量化标准**:
        *   从图像输入到3D模型生成的端到端流程成功率：>80%。
        *   所有Agent间通信无阻塞：100%。
        *   **能通过配置调整Agent间的协作顺序和权重。**
    *   **测试策略**: 运行端到端集成测试，覆盖所有Agent的交互和反馈循环，并验证编排策略的灵活性。

*   **任务 5.2: 全面系统集成测试、性能与安全性优化**
    *   **描述**: 执行全面的系统级集成测试、验收测试和性能测试。识别性能瓶颈并进行优化（如并发处理、缓存）。**进行基本的安全审查（如输入校验、权限控制）。**
    *   **交付物**:
        *   `tests/e2e_acceptance_tests.py`：端到端验收测试套件。
        *   性能测试报告。
        *   **安全审计报告（基础版）。**
    *   **量化标准**:
        *   测试套件通过率：>90%。
        *   图像到模型生成平均时间降低：>20%。
        *   内存和CPU使用率在可接受范围内。
        *   **发现并修复至少1个中度以上安全漏洞。**
    *   **测试策略**: 运行大规模端到端测试，包括各种图像输入和期望的复杂模型输出，并进行性能基准测试。进行渗透测试和安全漏洞扫描。

*   **任务 5.3: 文档、部署指南与监控方案编写**
    *   **描述**: 编写用户手册、API文档和部署指南，包括环境依赖、安装步骤、运行方式等。**设计并初步实现系统运行监控（日志、指标）。**
    *   **交付物**:
        *   `docs/user_manual.md`
        *   `docs/deployment_guide.md`
        *   `docs/monitoring_guide.md`：包含关键日志和指标的定义。
        *   `scripts/deploy_script.sh`：基础部署脚本。
    *   **量化标准**:
        *   文档覆盖率：>90%的核心功能和部署步骤。
        *   用户反馈文档清晰度评分：>4/5。
        *   **关键系统指标（如成功率、延迟、错误率）可被监控系统捕获。**
    *   **测试策略**: 邀请非开发人员进行文档审阅和部署测试。验证监控系统的有效性，确保日志和指标能正确上报。

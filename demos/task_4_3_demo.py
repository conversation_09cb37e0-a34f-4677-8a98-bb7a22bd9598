#!/usr/bin/env python3
"""
任务4.3演示脚本：增强规格到代码Agent：复杂功能与MCP支持

本脚本演示了CodeGenerationAgent的v2.0.0 schema支持，包括：
1. 复杂材质支持（7种类型）
2. Blender修改器支持（8种类型）
3. 动画关键帧支持
4. MCP结构支持（molecular, protein）
5. 增强的代码生成质量和性能

作者：Augment Agent
日期：2025-07-19
"""

import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.code_generation_agent import CodeGenerationAgent


def demo_complex_materials():
    """演示复杂材质支持"""
    print("🎨 演示复杂材质支持...")
    
    agent = CodeGenerationAgent()
    
    # 测试不同材质类型
    material_specs = [
        {
            "name": "PBR金属材质",
            "spec": {
                "schema_version": "v2.0.0",
                "model_info": {"name": "PBR Test", "description": "PBR material test"},
                "objects": [{
                    "id": "pbr_cube",
                    "name": "PBR Cube",
                    "geometry": {"type": "cube", "size": 2.0},
                    "material": {
                        "type": "pbr",
                        "name": "metal_material",
                        "color": {"r": 0.8, "g": 0.8, "b": 0.9, "a": 1.0},
                        "metallic": 0.9,
                        "roughness": 0.1
                    }
                }]
            }
        },
        {
            "name": "玻璃材质",
            "spec": {
                "schema_version": "v2.0.0",
                "model_info": {"name": "Glass Test", "description": "Glass material test"},
                "objects": [{
                    "id": "glass_sphere",
                    "name": "Glass Sphere",
                    "geometry": {"type": "sphere", "radius": 1.5},
                    "material": {
                        "type": "glass",
                        "name": "glass_material",
                        "color": {"r": 0.9, "g": 0.95, "b": 1.0, "a": 1.0},
                        "transmission": 1.0,
                        "ior": 1.5,
                        "roughness": 0.0
                    }
                }]
            }
        },
        {
            "name": "发光材质",
            "spec": {
                "schema_version": "v2.0.0",
                "model_info": {"name": "Emission Test", "description": "Emission material test"},
                "objects": [{
                    "id": "emission_plane",
                    "name": "Emission Plane",
                    "geometry": {"type": "plane", "size": 3.0},
                    "material": {
                        "type": "emission",
                        "name": "emission_material",
                        "color": {"r": 1.0, "g": 0.3, "b": 0.1, "a": 1.0},
                        "emission": {"r": 1.0, "g": 0.5, "b": 0.0},
                        "emission_strength": 5.0
                    }
                }]
            }
        }
    ]
    
    for material_test in material_specs:
        print(f"  ✓ 测试{material_test['name']}...")
        result = agent.generate_blender_code(material_test['spec'])
        print(f"    - 语法有效: {result.analysis_result.syntax_valid}")
        print(f"    - 质量等级: {result.analysis_result.quality_level.value}")
        print(f"    - 代码行数: {result.analysis_result.line_count}")


def demo_modifiers():
    """演示修改器支持"""
    print("\n🔧 演示修改器支持...")
    
    agent = CodeGenerationAgent()
    
    modifier_spec = {
        "schema_version": "v2.0.0",
        "model_info": {"name": "Modifier Demo", "description": "Modifier demonstration"},
        "objects": [{
            "id": "modified_cube",
            "name": "Modified Cube",
            "geometry": {"type": "cube", "size": 2.0},
            "material": {
                "type": "basic",
                "name": "basic_material",
                "color": {"r": 0.7, "g": 0.3, "b": 0.8, "a": 1.0}
            },
            "modifiers": [
                {
                    "type": "bevel",
                    "name": "edge_bevel",
                    "width": 0.1,
                    "segments": 3
                },
                {
                    "type": "array",
                    "name": "array_mod",
                    "count": 3,
                    "offset": {"x": 2.5, "y": 0.0, "z": 0.0}
                },
                {
                    "type": "subdivision_surface",
                    "name": "smooth_mod",
                    "levels": 2,
                    "render_levels": 3
                },
                {
                    "type": "mirror",
                    "name": "mirror_mod",
                    "use_axis": {"x": False, "y": True, "z": False},
                    "use_clip": True
                }
            ]
        }]
    }
    
    result = agent.generate_blender_code(modifier_spec)
    print(f"  ✓ 生成包含4个修改器的代码")
    print(f"    - 语法有效: {result.analysis_result.syntax_valid}")
    print(f"    - 质量等级: {result.analysis_result.quality_level.value}")
    print(f"    - 代码行数: {result.analysis_result.line_count}")
    
    # 检查修改器代码
    code = result.generated_code
    modifiers_found = []
    if "bevel" in code.lower():
        modifiers_found.append("Bevel")
    if "array" in code.lower():
        modifiers_found.append("Array")
    if "subdivision" in code.lower() or "subsurf" in code.lower():
        modifiers_found.append("Subdivision Surface")
    if "mirror" in code.lower():
        modifiers_found.append("Mirror")
    
    print(f"    - 检测到的修改器: {', '.join(modifiers_found)}")


def demo_animation():
    """演示动画关键帧支持"""
    print("\n🎬 演示动画关键帧支持...")
    
    agent = CodeGenerationAgent()
    
    animation_spec = {
        "schema_version": "v2.0.0",
        "model_info": {"name": "Animation Demo", "description": "Animation demonstration"},
        "objects": [{
            "id": "animated_sphere",
            "name": "Animated Sphere",
            "geometry": {"type": "sphere", "radius": 1.0},
            "material": {
                "type": "principled",
                "name": "animated_material",
                "color": {"r": 0.2, "g": 0.8, "b": 0.3, "a": 1.0},
                "metallic": 0.3,
                "roughness": 0.7
            },
            "animation": {
                "keyframes": [
                    {
                        "frame": 1,
                        "property": "location",
                        "value": {"x": -5.0, "y": 0.0, "z": 0.0},
                        "interpolation": "linear"
                    },
                    {
                        "frame": 25,
                        "property": "location",
                        "value": {"x": 0.0, "y": 0.0, "z": 0.0},
                        "interpolation": "linear"
                    },
                    {
                        "frame": 50,
                        "property": "location",
                        "value": {"x": 5.0, "y": 0.0, "z": 0.0},
                        "interpolation": "linear"
                    },
                    {
                        "frame": 25,
                        "property": "rotation",
                        "value": {"x": 0.0, "y": 3.14159, "z": 0.0},
                        "interpolation": "bezier"
                    },
                    {
                        "frame": 50,
                        "property": "scale",
                        "value": {"x": 2.0, "y": 2.0, "z": 2.0},
                        "interpolation": "linear"
                    }
                ]
            }
        }]
    }
    
    result = agent.generate_blender_code(animation_spec)
    print(f"  ✓ 生成包含5个关键帧的动画代码")
    print(f"    - 语法有效: {result.analysis_result.syntax_valid}")
    print(f"    - 质量等级: {result.analysis_result.quality_level.value}")
    print(f"    - 代码行数: {result.analysis_result.line_count}")
    
    # 检查动画代码
    code = result.generated_code
    if "keyframe_insert" in code:
        keyframe_count = code.count("keyframe_insert")
        print(f"    - 检测到{keyframe_count}个关键帧")
    if "frame_set" in code:
        frame_count = code.count("frame_set")
        print(f"    - 设置了{frame_count}个帧")


def demo_mcp_structures():
    """演示MCP结构支持"""
    print("\n🧬 演示MCP结构支持...")
    
    agent = CodeGenerationAgent()
    
    mcp_spec = {
        "schema_version": "v2.0.0",
        "model_info": {"name": "MCP Demo", "description": "MCP structures demonstration"},
        "objects": [],  # 只有MCP结构，没有常规对象
        "mcp_structures": [
            {
                "id": "caffeine_molecule",
                "name": "Caffeine Molecule",
                "structure_type": "molecular",
                "transform": {
                    "position": {"x": -3.0, "y": 0.0, "z": 0.0},
                    "rotation": {"x": 0.0, "y": 0.0, "z": 0.0},
                    "scale": {"x": 1.0, "y": 1.0, "z": 1.0}
                },
                "properties": {
                    "formula": "C8H10N4O2",
                    "molecular_weight": 194.19
                }
            },
            {
                "id": "insulin_protein",
                "name": "Insulin Protein",
                "structure_type": "protein",
                "transform": {
                    "position": {"x": 3.0, "y": 0.0, "z": 0.0},
                    "rotation": {"x": 0.0, "y": 1.57, "z": 0.0},
                    "scale": {"x": 0.5, "y": 0.5, "z": 0.5}
                },
                "properties": {
                    "pdb_id": "1ZNI",
                    "chain_count": 2
                }
            }
        ]
    }
    
    result = agent.generate_blender_code(mcp_spec)
    print(f"  ✓ 生成包含2个MCP结构的代码")
    print(f"    - 语法有效: {result.analysis_result.syntax_valid}")
    print(f"    - 质量等级: {result.analysis_result.quality_level.value}")
    print(f"    - 代码行数: {result.analysis_result.line_count}")
    
    # 检查MCP代码
    code = result.generated_code
    structures_found = []
    if "molecular" in code.lower():
        structures_found.append("分子结构")
    if "protein" in code.lower():
        structures_found.append("蛋白质结构")
    if "molecular_nodes" in code.lower():
        structures_found.append("Molecular Nodes集成")
    
    print(f"    - 检测到的结构: {', '.join(structures_found)}")


def demo_comprehensive_scene():
    """演示综合场景：包含所有v2功能"""
    print("\n🌟 演示综合场景：所有v2功能...")
    
    agent = CodeGenerationAgent()
    
    comprehensive_spec = {
        "schema_version": "v2.0.0",
        "model_info": {
            "name": "Comprehensive V2 Scene",
            "description": "A scene showcasing all v2.0.0 features",
            "complexity_level": "expert",
            "author": "Augment Agent",
            "version": "1.0.0"
        },
        "objects": [
            {
                "id": "hero_object",
                "name": "Hero Object",
                "geometry": {"type": "cube", "size": 2.0},
                "material": {
                    "type": "principled",
                    "name": "hero_material",
                    "color": {"r": 0.8, "g": 0.2, "b": 0.1, "a": 1.0},
                    "metallic": 0.7,
                    "roughness": 0.3,
                    "transmission": 0.1,
                    "ior": 1.5,
                    "emission": {"r": 0.1, "g": 0.05, "b": 0.0},
                    "emission_strength": 1.5
                },
                "modifiers": [
                    {"type": "bevel", "name": "hero_bevel", "width": 0.1, "segments": 3},
                    {"type": "subdivision_surface", "name": "hero_smooth", "levels": 2}
                ],
                "animation": {
                    "keyframes": [
                        {"frame": 1, "property": "location", "value": {"x": 0.0, "y": 0.0, "z": 0.0}},
                        {"frame": 60, "property": "rotation", "value": {"x": 0.0, "y": 6.28, "z": 0.0}},
                        {"frame": 120, "property": "scale", "value": {"x": 1.5, "y": 1.5, "z": 1.5}}
                    ]
                }
            }
        ],
        "mcp_structures": [
            {
                "id": "demo_molecule",
                "name": "Demo Molecule",
                "structure_type": "molecular",
                "transform": {"position": {"x": 5.0, "y": 0.0, "z": 0.0}}
            }
        ],
        "lighting": {
            "type": "three_point",
            "key_light": {"intensity": 5.0, "color": {"r": 1.0, "g": 0.9, "b": 0.8}},
            "fill_light": {"intensity": 2.0, "color": {"r": 0.8, "g": 0.9, "b": 1.0}},
            "rim_light": {"intensity": 3.0, "color": {"r": 1.0, "g": 1.0, "b": 1.0}}
        }
    }
    
    result = agent.generate_blender_code(comprehensive_spec)
    print(f"  ✓ 生成综合场景代码")
    print(f"    - 语法有效: {result.analysis_result.syntax_valid}")
    print(f"    - 质量等级: {result.analysis_result.quality_level.value}")
    print(f"    - 置信度: {result.confidence_score:.2f}")
    print(f"    - 代码行数: {result.analysis_result.line_count}")
    print(f"    - 生成时间: {result.generation_time:.3f}秒")
    
    return result


def print_feature_summary():
    """打印功能总结"""
    print("\n📊 任务4.3功能总结:")
    
    agent = CodeGenerationAgent()
    
    print(f"  🎨 材质类型 (v2): {len(agent.get_supported_material_types('v2.0.0'))}种")
    for material_type in agent.get_supported_material_types('v2.0.0'):
        print(f"    - {material_type}")
    
    print(f"  🔧 修改器类型: {len(agent.get_supported_modifier_types())}种")
    for modifier_type in agent.get_supported_modifier_types():
        print(f"    - {modifier_type}")
    
    print(f"  🧬 MCP结构类型: {len(agent.get_supported_mcp_types())}种")
    for mcp_type in agent.get_supported_mcp_types():
        print(f"    - {mcp_type}")
    
    print(f"  📐 几何体类型 (v2): {len(agent.get_supported_geometry_types('v2.0.0'))}种")
    for geometry_type in agent.get_supported_geometry_types('v2.0.0'):
        print(f"    - {geometry_type}")


def main():
    """主演示函数"""
    print("🚀 任务4.3演示：增强规格到代码Agent：复杂功能与MCP支持")
    print("=" * 70)
    
    try:
        # 演示各个功能模块
        demo_complex_materials()
        demo_modifiers()
        demo_animation()
        demo_mcp_structures()
        
        # 综合演示
        result = demo_comprehensive_scene()
        
        # 功能总结
        print_feature_summary()
        
        print("\n🎉 任务4.3演示完成！")
        print("✅ 所有v2.0.0 schema功能都已成功实现并验证")
        
        # 保存演示结果
        output_dir = Path("output/task_4_3_demo")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        with open(output_dir / "comprehensive_scene.py", "w", encoding="utf-8") as f:
            f.write(result.generated_code)
        
        print(f"📁 演示代码已保存到: {output_dir / 'comprehensive_scene.py'}")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

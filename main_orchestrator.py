"""
Main Orchestrator Agent for Blender 3D Model Generation AI Agent System

This module implements the main orchestrator that coordinates all agents
and manages the complete end-to-end workflow from image input to 3D model generation.

Features:
- AutoGen framework integration for agent communication
- Ray RLlib integration for decision optimization
- Inner loop (code-level) and outer loop (design-level) feedback mechanisms
- Configurable agent orchestration strategies
- Comprehensive error handling and recovery

Author: Augment Agent
Date: 2025-07-19
"""

import os
import json
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

# Core imports
from input_module.image_handler import ImageHandler, ImageMetadata
from agents.image_analysis_agent import ImageAnalysisAgent, ImageAnalysisResult, AnalysisGranularity
from agents.knowledge_agent import KnowledgeAgent
from agents.spec_generation_agent import SpecGenerationAgent, SpecGenerationResult, SpecGenerationConfig, ValidationLevel
from agents.code_generation_agent import CodeGenerationAgent, CodeGenerationResult, CodeGenerationConfig
from blender_interface.blender_executor import BlenderExecutor, BlenderOutput
from agents.validator_debugger_agent import ValidatorDebuggerAgent, ValidationResult
from agents.visual_critic_agent import VisualCriticAgent, VisualCriticResult

# AutoGen and RL imports
from agents.autogen_om_poc import AgentCommunicationProtocol, RLEnhancedAgent

# MLOps imports
from mlops.experiment_tracker import ExperimentTracker
from mlops.model_registry import ModelRegistry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WorkflowStage(Enum):
    """Workflow stages for tracking progress."""
    INITIALIZATION = "initialization"
    IMAGE_PROCESSING = "image_processing"
    IMAGE_ANALYSIS = "image_analysis"
    SPEC_GENERATION = "spec_generation"
    CODE_GENERATION = "code_generation"
    BLENDER_EXECUTION = "blender_execution"
    VALIDATION_DEBUG = "validation_debug"
    VISUAL_CRITIQUE = "visual_critique"
    COMPLETED = "completed"
    FAILED = "failed"


class LoopType(Enum):
    """Types of feedback loops."""
    INNER_LOOP = "inner_loop"  # Code-level corrections
    OUTER_LOOP = "outer_loop"  # Design-level corrections


@dataclass
class OrchestrationConfig:
    """Configuration for orchestration behavior."""
    max_inner_loop_iterations: int = 3
    max_outer_loop_iterations: int = 2
    enable_visual_critique: bool = True
    enable_rl_optimization: bool = True
    auto_save_results: bool = True
    output_directory: str = "orchestration_output"
    timeout_seconds: int = 300
    quality_threshold: float = 0.7


@dataclass
class TaskContext:
    """Context information for a modeling task."""
    task_id: str
    image_path: str
    user_preferences: Dict[str, Any]
    model_name: Optional[str] = None
    description: Optional[str] = None
    created_at: Optional[datetime] = None


@dataclass
class WorkflowState:
    """Current state of the workflow."""
    task_id: str
    current_stage: WorkflowStage
    inner_loop_count: int = 0
    outer_loop_count: int = 0
    start_time: Optional[datetime] = None
    last_update: Optional[datetime] = None
    error_count: int = 0
    intermediate_results: Dict[str, Any] = None
    stages_completed: List[WorkflowStage] = None

    def __post_init__(self):
        if self.intermediate_results is None:
            self.intermediate_results = {}
        if self.stages_completed is None:
            self.stages_completed = []


@dataclass
class OrchestrationResult:
    """Final result of the orchestration process."""
    task_id: str
    success: bool
    final_model_path: Optional[str] = None
    final_render_path: Optional[str] = None
    total_time: float = 0.0
    stages_completed: List[WorkflowStage] = None
    inner_loop_iterations: int = 0
    outer_loop_iterations: int = 0
    error_messages: List[str] = None
    quality_score: Optional[float] = None
    
    def __post_init__(self):
        if self.stages_completed is None:
            self.stages_completed = []
        if self.error_messages is None:
            self.error_messages = []


class OrchestratorAgent:
    """
    Main orchestrator agent that coordinates all other agents and manages
    the complete end-to-end workflow.
    """
    
    def __init__(self, 
                 config: OrchestrationConfig = None,
                 workspace_dir: str = "orchestration_workspace"):
        """Initialize the orchestrator agent."""
        self.config = config or OrchestrationConfig()
        self.workspace_dir = Path(workspace_dir)
        self.workspace_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize communication protocol
        self.communication = AgentCommunicationProtocol()
        
        # Initialize all agents
        self._initialize_agents()
        
        # Initialize MLOps components
        if self.config.auto_save_results:
            self.experiment_tracker = ExperimentTracker(
                experiments_dir=self.workspace_dir / "experiments"
            )
        else:
            self.experiment_tracker = None
        
        # Track active tasks
        self.active_tasks: Dict[str, WorkflowState] = {}
        
        logger.info(f"OrchestratorAgent initialized with workspace: {self.workspace_dir}")
    
    def _initialize_agents(self):
        """Initialize all required agents."""
        try:
            # Core processing agents
            self.image_handler = ImageHandler()
            self.image_analysis_agent = ImageAnalysisAgent()
            self.knowledge_agent = KnowledgeAgent()
            
            # Generation agents
            spec_config = SpecGenerationConfig(
                validation_level=ValidationLevel.PYDANTIC,
                enable_knowledge_context=True
            )
            self.spec_generation_agent = SpecGenerationAgent(
                config=spec_config,
                knowledge_agent=self.knowledge_agent
            )
            
            code_config = CodeGenerationConfig(
                enable_static_analysis=True,
                use_knowledge_agent=True
            )
            self.code_generation_agent = CodeGenerationAgent(
                config=code_config,
                knowledge_agent=self.knowledge_agent
            )
            
            # Execution and validation agents
            self.blender_executor = BlenderExecutor()
            self.validator_debugger_agent = ValidatorDebuggerAgent()
            
            if self.config.enable_visual_critique:
                self.visual_critic_agent = VisualCriticAgent()
            else:
                self.visual_critic_agent = None
            
            # Load knowledge base
            self.knowledge_agent.load_knowledge_base()
            
            logger.info("All agents initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize agents: {e}")
            raise
    
    def orchestrate_task(self, 
                        image_path: str,
                        user_preferences: Dict[str, Any] = None,
                        model_name: str = None,
                        description: str = None) -> OrchestrationResult:
        """
        Orchestrate a complete 3D model generation task.
        
        Args:
            image_path: Path to input image
            user_preferences: User preferences for generation
            model_name: Optional name for the model
            description: Optional description
            
        Returns:
            OrchestrationResult with final results
        """
        # Create task context
        task_id = str(uuid.uuid4())
        task_context = TaskContext(
            task_id=task_id,
            image_path=image_path,
            user_preferences=user_preferences or {},
            model_name=model_name or f"Generated_Model_{task_id[:8]}",
            description=description,
            created_at=datetime.now()
        )
        
        # Initialize workflow state
        workflow_state = WorkflowState(
            task_id=task_id,
            current_stage=WorkflowStage.INITIALIZATION,
            start_time=datetime.now()
        )
        
        self.active_tasks[task_id] = workflow_state
        
        logger.info(f"Starting orchestration for task {task_id}")
        
        try:
            # Start experiment tracking
            if self.experiment_tracker:
                experiment_id = self.experiment_tracker.create_experiment(
                    name=f"orchestration_{task_id[:8]}",
                    description=f"End-to-end model generation from {image_path}",
                    tags=["orchestration", "end-to-end"]
                )
                workflow_state.intermediate_results["experiment_id"] = experiment_id
            
            # Execute main workflow
            result = self._execute_main_workflow(task_context, workflow_state)
            
            # Finalize experiment tracking
            if self.experiment_tracker and "experiment_id" in workflow_state.intermediate_results:
                self.experiment_tracker.log_metrics(
                    experiment_id=workflow_state.intermediate_results["experiment_id"],
                    metrics={
                        "success": result.success,
                        "total_time": result.total_time,
                        "inner_loop_iterations": result.inner_loop_iterations,
                        "outer_loop_iterations": result.outer_loop_iterations,
                        "quality_score": result.quality_score or 0.0
                    }
                )
                self.experiment_tracker.finish_experiment(
                    workflow_state.intermediate_results["experiment_id"]
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Orchestration failed for task {task_id}: {e}")
            
            # Create failure result
            result = OrchestrationResult(
                task_id=task_id,
                success=False,
                total_time=time.time() - workflow_state.start_time.timestamp(),
                error_messages=[str(e)]
            )
            
            workflow_state.current_stage = WorkflowStage.FAILED
            
            return result
        
        finally:
            # Clean up
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]

    def _execute_main_workflow(self,
                              task_context: TaskContext,
                              workflow_state: WorkflowState) -> OrchestrationResult:
        """Execute the main workflow pipeline."""
        start_time = time.time()

        try:
            # Stage 1: Image Processing
            workflow_state.current_stage = WorkflowStage.IMAGE_PROCESSING
            workflow_state.last_update = datetime.now()

            logger.info(f"Stage 1: Processing image {task_context.image_path}")
            processed_image = self.image_handler.process_image(task_context.image_path)
            workflow_state.intermediate_results["processed_image"] = processed_image
            workflow_state.stages_completed.append(WorkflowStage.IMAGE_PROCESSING)

            # Stage 2: Image Analysis
            workflow_state.current_stage = WorkflowStage.IMAGE_ANALYSIS
            workflow_state.last_update = datetime.now()

            logger.info("Stage 2: Analyzing image content")
            analysis_result = self.image_analysis_agent.analyze_image(
                processed_image.processed_path,
                granularity=AnalysisGranularity.DETAILED
            )
            workflow_state.intermediate_results["analysis_result"] = analysis_result
            workflow_state.stages_completed.append(WorkflowStage.IMAGE_ANALYSIS)

            # Stage 3: Specification Generation (with outer loop)
            spec_result = self._execute_spec_generation_with_outer_loop(
                task_context, workflow_state, analysis_result
            )

            if not spec_result.validation_passed:
                raise Exception("Specification generation failed validation")

            # Stage 4: Code Generation (with inner loop)
            final_model_path, final_render_path = self._execute_code_generation_with_inner_loop(
                task_context, workflow_state, spec_result
            )

            # Calculate final results
            total_time = time.time() - start_time

            # Calculate quality score if visual critique was used
            quality_score = None
            if "final_critique_result" in workflow_state.intermediate_results:
                critique_result = workflow_state.intermediate_results["final_critique_result"]
                quality_score = critique_result.overall_consistency_score

            # Create success result
            result = OrchestrationResult(
                task_id=task_context.task_id,
                success=True,
                final_model_path=final_model_path,
                final_render_path=final_render_path,
                total_time=total_time,
                stages_completed=workflow_state.stages_completed.copy(),
                inner_loop_iterations=workflow_state.inner_loop_count,
                outer_loop_iterations=workflow_state.outer_loop_count,
                quality_score=quality_score
            )

            workflow_state.current_stage = WorkflowStage.COMPLETED

            logger.info(f"Workflow completed successfully in {total_time:.2f}s")
            return result

        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")

            total_time = time.time() - start_time
            result = OrchestrationResult(
                task_id=task_context.task_id,
                success=False,
                total_time=total_time,
                stages_completed=workflow_state.stages_completed.copy(),
                inner_loop_iterations=workflow_state.inner_loop_count,
                outer_loop_iterations=workflow_state.outer_loop_count,
                error_messages=[str(e)]
            )

            workflow_state.current_stage = WorkflowStage.FAILED
            return result

    def _execute_spec_generation_with_outer_loop(self,
                                               task_context: TaskContext,
                                               workflow_state: WorkflowState,
                                               analysis_result: ImageAnalysisResult) -> SpecGenerationResult:
        """Execute specification generation with outer loop feedback."""
        workflow_state.current_stage = WorkflowStage.SPEC_GENERATION
        workflow_state.last_update = datetime.now()

        logger.info("Stage 3: Generating 3D model specification")

        # Initial specification generation
        spec_result = self.spec_generation_agent.generate_specification(
            image_analysis_result=analysis_result,
            model_name=task_context.model_name,
            user_preferences=task_context.user_preferences
        )

        workflow_state.intermediate_results["spec_result"] = spec_result
        workflow_state.stages_completed.append(WorkflowStage.SPEC_GENERATION)

        # Outer loop: Visual critique and specification refinement
        if self.config.enable_visual_critique and self.visual_critic_agent:
            spec_result = self._outer_loop_refinement(
                task_context, workflow_state, analysis_result, spec_result
            )

        return spec_result

    def _outer_loop_refinement(self,
                             task_context: TaskContext,
                             workflow_state: WorkflowState,
                             analysis_result: ImageAnalysisResult,
                             initial_spec_result: SpecGenerationResult) -> SpecGenerationResult:
        """Execute outer loop refinement with visual critique."""
        current_spec_result = initial_spec_result

        for iteration in range(self.config.max_outer_loop_iterations):
            workflow_state.outer_loop_count = iteration + 1

            logger.info(f"Outer loop iteration {iteration + 1}")

            # Generate a quick preview to get visual feedback
            try:
                # Generate temporary code for preview
                temp_code_result = self.code_generation_agent.generate_blender_code(
                    current_spec_result.specification
                )

                if temp_code_result.generated_code:
                    # Execute for preview
                    temp_execution_result = self.blender_executor.execute_script(
                        temp_code_result.generated_code,
                        output_dir="demo_output/temp_preview"
                    )

                    if temp_execution_result.status.value == "success" and temp_execution_result.output_files:
                        # Get visual critique
                        workflow_state.current_stage = WorkflowStage.VISUAL_CRITIQUE

                        # For now, skip visual critique since we don't have automatic rendering
                        # In a full implementation, we would add rendering capability to BlenderExecutor
                        logger.info("Skipping visual critique - automatic rendering not implemented")
                        break
                    else:
                        logger.warning("Failed to generate preview for visual critique")
                        break
                else:
                    logger.warning("Failed to generate temporary code for preview")
                    break

            except Exception as e:
                logger.warning(f"Outer loop iteration {iteration + 1} failed: {e}")
                break

        workflow_state.current_stage = WorkflowStage.SPEC_GENERATION
        return current_spec_result

    def _execute_code_generation_with_inner_loop(self,
                                               task_context: TaskContext,
                                               workflow_state: WorkflowState,
                                               spec_result: SpecGenerationResult) -> Tuple[str, Optional[str]]:
        """Execute code generation with inner loop error correction."""
        workflow_state.current_stage = WorkflowStage.CODE_GENERATION
        workflow_state.last_update = datetime.now()

        logger.info("Stage 4: Generating Blender Python code")

        # Initial code generation
        code_result = self.code_generation_agent.generate_blender_code(
            spec_result.specification
        )

        if not code_result.generated_code:
            raise Exception(f"Initial code generation failed: No code generated")

        workflow_state.intermediate_results["code_result"] = code_result
        workflow_state.stages_completed.append(WorkflowStage.CODE_GENERATION)

        # Inner loop: Code execution and debugging
        final_model_path, final_render_path = self._inner_loop_execution(
            task_context, workflow_state, code_result
        )

        return final_model_path, final_render_path

    def _inner_loop_execution(self,
                            task_context: TaskContext,
                            workflow_state: WorkflowState,
                            initial_code_result: CodeGenerationResult) -> Tuple[str, Optional[str]]:
        """Execute inner loop with error correction."""
        current_code = initial_code_result.generated_code

        for iteration in range(self.config.max_inner_loop_iterations):
            workflow_state.inner_loop_count = iteration + 1
            workflow_state.current_stage = WorkflowStage.BLENDER_EXECUTION
            workflow_state.last_update = datetime.now()

            logger.info(f"Inner loop iteration {iteration + 1}: Executing Blender script")

            # Execute Blender script
            execution_result = self.blender_executor.execute_script(
                current_code,
                output_dir=f"demo_output/{task_context.model_name}_iter_{iteration + 1}"
            )

            workflow_state.intermediate_results[f"execution_result_{iteration + 1}"] = execution_result

            if execution_result.status.value == "success":
                # Success! Save final results
                workflow_state.stages_completed.append(WorkflowStage.BLENDER_EXECUTION)

                logger.info(f"Blender execution successful on iteration {iteration + 1}")
                output_path = execution_result.output_files[0] if execution_result.output_files else None
                render_path = None  # BlenderExecutor doesn't automatically render
                return output_path, render_path

            else:
                # Execution failed, try to debug and fix
                logger.warning(f"Blender execution failed on iteration {iteration + 1}: {execution_result.stderr}")

                if iteration < self.config.max_inner_loop_iterations - 1:
                    # Try to fix the code
                    current_code = self._debug_and_fix_code(
                        workflow_state, current_code, execution_result
                    )

                    if current_code is None:
                        logger.error("Code debugging failed, cannot continue")
                        break
                else:
                    logger.error("Maximum inner loop iterations reached")

        # If we get here, all iterations failed
        raise Exception(f"Code execution failed after {self.config.max_inner_loop_iterations} attempts")

    def _debug_and_fix_code(self,
                          workflow_state: WorkflowState,
                          failed_code: str,
                          execution_result: BlenderOutput) -> Optional[str]:
        """Debug and fix failed code using ValidatorDebuggerAgent."""
        workflow_state.current_stage = WorkflowStage.VALIDATION_DEBUG
        workflow_state.last_update = datetime.now()

        logger.info("Debugging failed code...")

        try:
            # Use validator/debugger agent to analyze and fix
            validation_result = self.validator_debugger_agent.validate_and_debug(
                code=failed_code,
                error_log=execution_result.stderr,
                execution_context={
                    "return_code": execution_result.return_code,
                    "execution_time": execution_result.execution_time
                }
            )

            workflow_state.intermediate_results["validation_result"] = validation_result

            if validation_result.fix_applied and validation_result.fixed_code:
                logger.info(f"Code fixes applied successfully")
                workflow_state.stages_completed.append(WorkflowStage.VALIDATION_DEBUG)
                return validation_result.fixed_code
            else:
                logger.warning("No fixes could be applied to the code")
                return None

        except Exception as e:
            logger.error(f"Code debugging failed: {e}")
            return None

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a task."""
        if task_id not in self.active_tasks:
            return None

        workflow_state = self.active_tasks[task_id]

        return {
            "task_id": task_id,
            "current_stage": workflow_state.current_stage.value,
            "inner_loop_count": workflow_state.inner_loop_count,
            "outer_loop_count": workflow_state.outer_loop_count,
            "start_time": workflow_state.start_time.isoformat() if workflow_state.start_time else None,
            "last_update": workflow_state.last_update.isoformat() if workflow_state.last_update else None,
            "stages_completed": [stage.value for stage in workflow_state.stages_completed],
            "error_count": workflow_state.error_count
        }

    def list_active_tasks(self) -> List[Dict[str, Any]]:
        """List all currently active tasks."""
        return [self.get_task_status(task_id) for task_id in self.active_tasks.keys()]

    def cancel_task(self, task_id: str) -> bool:
        """Cancel an active task."""
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
            logger.info(f"Task {task_id} cancelled")
            return True
        return False

    def save_orchestration_config(self, config_path: str):
        """Save current orchestration configuration."""
        config_dict = asdict(self.config)
        with open(config_path, 'w') as f:
            json.dump(config_dict, f, indent=2)
        logger.info(f"Configuration saved to {config_path}")

    def load_orchestration_config(self, config_path: str):
        """Load orchestration configuration from file."""
        with open(config_path, 'r') as f:
            config_dict = json.load(f)

        self.config = OrchestrationConfig(**config_dict)
        logger.info(f"Configuration loaded from {config_path}")


def create_default_orchestrator(workspace_dir: str = "orchestration_workspace") -> OrchestratorAgent:
    """Create an orchestrator with default configuration."""
    config = OrchestrationConfig(
        max_inner_loop_iterations=3,
        max_outer_loop_iterations=2,
        enable_visual_critique=True,
        enable_rl_optimization=True,
        auto_save_results=True,
        quality_threshold=0.7
    )

    return OrchestratorAgent(config=config, workspace_dir=workspace_dir)

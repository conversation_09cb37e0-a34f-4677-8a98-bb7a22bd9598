"""
Test suite for Task 4.5: Ray RLlib 策略训练与持续优化

This module contains comprehensive tests for RL training pipeline including
trajectory collection, policy optimization, and MLOps integration.
"""

import unittest
import sys
import os
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from rl_training.training_config import (
    TrainingConfig, TrainingMode, OptimizationAlgorithm,
    HyperparameterConfig, EvaluationConfig
)
from rl_training.policy_optimizer import OptimizationConfig
from rl_training.trajectory_collector import (
    TrajectoryCollector, TrajectoryData, StateData, ActionData, RewardData
)
from rl_training.enhanced_agent_base import EnhancedAgentBase, AgentAction, AgentState


class TestTrainingConfig(unittest.TestCase):
    """Test training configuration classes."""
    
    def test_training_config_creation(self):
        """Test training config creation with defaults."""
        config = TrainingConfig()
        
        self.assertEqual(config.experiment_name, "rl_strategy_training")
        self.assertEqual(config.training_mode, TrainingMode.OFFLINE)
        self.assertEqual(config.algorithm, OptimizationAlgorithm.PPO)
        self.assertEqual(config.num_workers, 4)
        self.assertIsInstance(config.hyperparameters, HyperparameterConfig)
        self.assertIsInstance(config.evaluation, EvaluationConfig)
    
    def test_hyperparameter_config(self):
        """Test hyperparameter configuration."""
        hyper_config = HyperparameterConfig(
            learning_rate=0.001,
            gamma=0.95,
            train_batch_size=2000
        )
        
        self.assertEqual(hyper_config.learning_rate, 0.001)
        self.assertEqual(hyper_config.gamma, 0.95)
        self.assertEqual(hyper_config.train_batch_size, 2000)
        self.assertEqual(len(hyper_config.hidden_layers), 2)
    
    def test_config_serialization(self):
        """Test config save/load functionality."""
        config = TrainingConfig(
            experiment_name="test_experiment",
            max_training_iterations=500
        )
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config.save(f.name)
            
            # Load and verify
            loaded_config = TrainingConfig.load(f.name)
            self.assertEqual(loaded_config.experiment_name, "test_experiment")
            self.assertEqual(loaded_config.max_training_iterations, 500)
            
            # Cleanup
            os.unlink(f.name)


class TestTrajectoryCollector(unittest.TestCase):
    """Test trajectory data collection functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.collector = TrajectoryCollector(
            storage_path=os.path.join(self.temp_dir, "trajectories"),
            max_trajectories=100,
            auto_save=False  # Disable for testing
        )
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_trajectory_lifecycle(self):
        """Test complete trajectory lifecycle."""
        # Start trajectory
        trajectory_id = self.collector.start_trajectory(
            agent_type="test_agent",
            initial_context={"test": "context"}
        )
        
        self.assertIsNotNone(trajectory_id)
        self.assertIn(trajectory_id, self.collector.active_trajectories)
        
        # Add state
        state_id = self.collector.add_state(
            trajectory_id=trajectory_id,
            observation=[1.0, 2.0, 3.0],
            task_type="test_task",
            task_progress=0.5,
            context_complexity=2.0
        )
        
        self.assertIsNotNone(state_id)
        
        # Add action
        action_id = self.collector.add_action(
            trajectory_id=trajectory_id,
            action_type="test_action",
            action_value=1,
            context={"action_context": "test"}
        )
        
        self.assertIsNotNone(action_id)
        
        # Add reward
        self.collector.add_reward(
            trajectory_id=trajectory_id,
            reward_value=0.8,
            reward_components={"base": 0.6, "bonus": 0.2},
            success=True
        )
        
        # End trajectory
        completed_trajectory = self.collector.end_trajectory(
            trajectory_id=trajectory_id,
            success=True,
            task_completed=True,
            final_context={"result": "success"}
        )
        
        self.assertIsNotNone(completed_trajectory)
        self.assertEqual(completed_trajectory.success, True)
        self.assertEqual(completed_trajectory.total_reward, 0.8)
        self.assertEqual(len(completed_trajectory.states), 1)
        self.assertEqual(len(completed_trajectory.actions), 1)
        self.assertEqual(len(completed_trajectory.rewards), 1)
    
    def test_trajectory_filtering(self):
        """Test trajectory filtering functionality."""
        # Create multiple trajectories
        for i in range(5):
            trajectory_id = self.collector.start_trajectory(
                agent_type=f"agent_{i % 2}",  # Two different agent types
                initial_context={"index": i}
            )
            
            self.collector.add_reward(
                trajectory_id=trajectory_id,
                reward_value=i * 0.2,
                reward_components={"base": i * 0.2},
                success=i % 2 == 0  # Alternate success/failure
            )
            
            self.collector.end_trajectory(
                trajectory_id=trajectory_id,
                success=i % 2 == 0,
                task_completed=True,
                final_context={}
            )
        
        # Test filtering by agent type
        agent_0_trajectories = self.collector.get_trajectories(agent_type="agent_0")
        self.assertEqual(len(agent_0_trajectories), 3)  # indices 0, 2, 4
        
        # Test filtering by success
        successful_trajectories = self.collector.get_trajectories(success_only=True)
        self.assertEqual(len(successful_trajectories), 3)  # indices 0, 2, 4
        
        # Test filtering by minimum reward
        high_reward_trajectories = self.collector.get_trajectories(min_reward=0.5)
        self.assertEqual(len(high_reward_trajectories), 2)  # indices 3, 4
    
    def test_statistics_calculation(self):
        """Test trajectory statistics calculation."""
        # Create test trajectories
        for i in range(3):
            trajectory_id = self.collector.start_trajectory(
                agent_type="test_agent",
                initial_context={}
            )
            
            self.collector.add_reward(
                trajectory_id=trajectory_id,
                reward_value=i * 0.5,
                reward_components={"base": i * 0.5},
                success=i > 0
            )
            
            self.collector.end_trajectory(
                trajectory_id=trajectory_id,
                success=i > 0,
                task_completed=True,
                final_context={}
            )
        
        stats = self.collector.get_statistics()
        
        self.assertEqual(stats["total_trajectories"], 3)
        self.assertAlmostEqual(stats["success_rate"], 2/3, places=2)
        self.assertAlmostEqual(stats["avg_reward"], 0.5, places=2)
        self.assertIn("test_agent", stats["agent_types"])


class TestEnhancedAgentBase(unittest.TestCase):
    """Test enhanced agent base class."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.collector = TrajectoryCollector(
            storage_path=os.path.join(self.temp_dir, "trajectories"),
            auto_save=False
        )
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_agent_initialization(self):
        """Test agent initialization."""
        class TestAgent(EnhancedAgentBase):
            def execute_task(self, task_input):
                return f"Result for {task_input}"
        
        agent = TestAgent(
            agent_type="test_agent",
            trajectory_collector=self.collector,
            enable_trajectory_collection=True
        )
        
        self.assertEqual(agent.agent_type, "test_agent")
        self.assertTrue(agent.enable_trajectory_collection)
        self.assertEqual(agent.trajectory_collector, self.collector)
        self.assertIsNone(agent.current_trajectory_id)
    
    def test_trajectory_recording(self):
        """Test trajectory recording functionality."""
        class TestAgent(EnhancedAgentBase):
            def execute_task(self, task_input):
                # Record state
                self.record_state(
                    observation=[1.0, 2.0],
                    task_type="test",
                    task_progress=0.5,
                    context_complexity=1.0
                )
                
                # Record action
                self.record_action(
                    action_type="test_action",
                    action_value="test_value",
                    context={"input": task_input}
                )
                
                # Record reward
                self.record_reward(
                    reward_value=0.8,
                    reward_components={"base": 0.8},
                    success=True
                )
                
                return f"Result for {task_input}"
        
        agent = TestAgent(
            agent_type="test_agent",
            trajectory_collector=self.collector
        )
        
        # Execute task with tracking
        result = agent.execute_task_with_tracking("test_input")
        
        self.assertEqual(result, "Result for test_input")
        self.assertEqual(len(agent.state_history), 1)
        self.assertEqual(len(agent.action_history), 1)
        self.assertEqual(len(agent.reward_history), 2)  # One from task execution, one from execute_task_with_tracking
        
        # Check trajectory was completed
        self.assertIsNone(agent.current_trajectory_id)
        self.assertEqual(len(self.collector.completed_trajectories), 1)
    
    def test_performance_metrics(self):
        """Test performance metrics calculation."""
        class TestAgent(EnhancedAgentBase):
            def execute_task(self, task_input):
                return task_input
        
        agent = TestAgent("test_agent")
        
        # Add some test data
        agent.record_reward(0.8, {"base": 0.8}, True)
        agent.record_reward(0.6, {"base": 0.6}, True)
        agent.record_reward(-0.2, {"base": -0.2}, False)
        
        agent.record_action("test", "value1", {}, {"timestamp": 1.0})
        agent.record_action("test", "value2", {}, {"timestamp": 1.5})
        agent.record_action("test", "value3", {}, {"timestamp": 2.0})
        
        metrics = agent.calculate_performance_metrics()
        
        self.assertAlmostEqual(metrics["avg_reward"], 0.4, places=2)
        self.assertAlmostEqual(metrics["success_rate"], 2/3, places=2)
        self.assertEqual(metrics["total_actions"], 3)
        self.assertAlmostEqual(metrics["total_reward"], 1.2, places=2)


class TestRLTrainingIntegration(unittest.TestCase):
    """Test RL training integration."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    @patch('rl_training.policy_optimizer.HAS_RAY', False)
    def test_training_without_ray(self):
        """Test training behavior when Ray is not available."""
        from rl_training.policy_optimizer import PolicyOptimizer
        from rl_training.trajectory_collector import TrajectoryCollector
        
        config = TrainingConfig()
        collector = TrajectoryCollector(
            storage_path=os.path.join(self.temp_dir, "trajectories"),
            auto_save=False
        )
        
        with self.assertRaises(ImportError):
            PolicyOptimizer(config, collector)
    
    def test_training_config_validation(self):
        """Test training configuration validation."""
        # Test valid config
        config = TrainingConfig(
            min_trajectories_required=10,
            max_training_iterations=100
        )
        
        self.assertEqual(config.min_trajectories_required, 10)
        self.assertEqual(config.max_training_iterations, 100)
        
        # Test default values
        default_config = TrainingConfig()
        self.assertGreater(default_config.min_trajectories_required, 0)
        self.assertGreater(default_config.max_training_iterations, 0)


def run_rl_training_tests():
    """Run all RL training tests and return results."""
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestTrainingConfig,
        TestTrajectoryCollector,
        TestEnhancedAgentBase,
        TestRLTrainingIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result


if __name__ == "__main__":
    print("Running Task 4.5 RL Training Tests...")
    print("=" * 50)
    
    result = run_rl_training_tests()
    
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ All RL training tests passed!")
    else:
        print("❌ Some tests failed:")
        for failure in result.failures:
            print(f"   FAIL: {failure[0]}")
        for error in result.errors:
            print(f"   ERROR: {error[0]}")
    
    print(f"\nTest Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")

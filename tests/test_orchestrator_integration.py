"""
Comprehensive integration tests for OrchestratorAgent (Task 5.1)

This test suite verifies the complete end-to-end orchestration functionality
including workflow management, error handling, and performance characteristics.

Author: Augment Agent
Date: 2025-07-19
"""

import unittest
import os
import sys
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from main_orchestrator import (
    OrchestratorAgent,
    OrchestrationConfig,
    WorkflowStage,
    LoopType,
    TaskContext,
    WorkflowState,
    OrchestrationResult,
    create_default_orchestrator
)

# Import agent classes for mocking
from agents.image_analysis_agent import ImageAnalysisResult, DetectedShape, ShapeType, BoundingBox, ColorInfo, AnalysisGranularity
from agents.spec_generation_agent import SpecGenerationResult
from agents.code_generation_agent import CodeGenerationResult
from blender_interface.blender_executor import BlenderOutput, BlenderExecutionStatus
from agents.validator_debugger_agent import ValidationResult
from agents.visual_critic_agent import VisualCriticResult


class TestOrchestratorIntegration(unittest.TestCase):
    """Test orchestrator integration functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_dir = tempfile.mkdtemp()
        self.config = OrchestrationConfig(
            max_inner_loop_iterations=2,
            max_outer_loop_iterations=1,
            enable_visual_critique=False,  # Disable for faster testing
            enable_rl_optimization=False,
            auto_save_results=False,
            timeout_seconds=60
        )
        
        # Create test image
        self.test_image_path = os.path.join(self.test_dir, "test_image.png")
        with open(self.test_image_path, 'w') as f:
            f.write("dummy image content")
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_orchestrator_initialization(self):
        """Test orchestrator initialization."""
        print("\n--- Testing Orchestrator Initialization ---")

        # Mock BlenderExecutor to avoid Blender dependency
        with patch('main_orchestrator.BlenderExecutor') as mock_blender:
            mock_blender.return_value = Mock()

            orchestrator = OrchestratorAgent(
                config=self.config,
                workspace_dir=self.test_dir
            )
        
            # Verify initialization
            self.assertIsNotNone(orchestrator.config)
            self.assertIsNotNone(orchestrator.communication)
            self.assertIsNotNone(orchestrator.image_handler)
            self.assertIsNotNone(orchestrator.image_analysis_agent)
            self.assertIsNotNone(orchestrator.knowledge_agent)
            self.assertIsNotNone(orchestrator.spec_generation_agent)
            self.assertIsNotNone(orchestrator.code_generation_agent)
            self.assertIsNotNone(orchestrator.blender_executor)
            self.assertIsNotNone(orchestrator.validator_debugger_agent)

            # Verify workspace creation
            self.assertTrue(os.path.exists(orchestrator.workspace_dir))

            print("✅ Orchestrator initialized successfully")
    
    def test_configuration_management(self):
        """Test configuration save/load functionality."""
        print("\n--- Testing Configuration Management ---")

        # Mock BlenderExecutor to avoid Blender dependency
        with patch('main_orchestrator.BlenderExecutor') as mock_blender:
            mock_blender.return_value = Mock()

            orchestrator = OrchestratorAgent(
                config=self.config,
                workspace_dir=self.test_dir
            )
        
            # Save configuration
            config_path = os.path.join(self.test_dir, "test_config.json")
            orchestrator.save_orchestration_config(config_path)

            self.assertTrue(os.path.exists(config_path))

            # Load configuration
            with patch('main_orchestrator.BlenderExecutor') as mock_blender2:
                mock_blender2.return_value = Mock()
                new_orchestrator = create_default_orchestrator(self.test_dir)
                new_orchestrator.load_orchestration_config(config_path)

                # Verify loaded configuration
                self.assertEqual(
                    new_orchestrator.config.max_inner_loop_iterations,
                    self.config.max_inner_loop_iterations
                )
                self.assertEqual(
                    new_orchestrator.config.max_outer_loop_iterations,
                    self.config.max_outer_loop_iterations
                )

                print("✅ Configuration management working correctly")
    
    def test_task_context_creation(self):
        """Test task context creation and management."""
        print("\n--- Testing Task Context Creation ---")

        # Mock BlenderExecutor to avoid Blender dependency
        with patch('main_orchestrator.BlenderExecutor') as mock_blender:
            mock_blender.return_value = Mock()

            orchestrator = OrchestratorAgent(
                config=self.config,
                workspace_dir=self.test_dir
            )
        
            # Test task context creation (this happens internally in orchestrate_task)
            user_preferences = {"units": "meters", "quality": "high"}
            model_name = "Test_Model"
            description = "Test model description"

            # We'll test this indirectly through the orchestrate_task method
            # by mocking the agents to avoid actual execution

            with patch.object(orchestrator.image_handler, 'process_image') as mock_process:
                with patch.object(orchestrator.image_analysis_agent, 'analyze_image') as mock_analyze:
                    with patch.object(orchestrator.spec_generation_agent, 'generate_specification') as mock_spec:
                        with patch.object(orchestrator.code_generation_agent, 'generate_blender_code') as mock_code:
                            with patch.object(orchestrator.blender_executor, 'execute_script') as mock_execute:

                                # Setup mocks
                                mock_process.return_value = Mock(processed_path="processed.png")
                                mock_analyze.return_value = self._create_mock_analysis_result()
                                mock_spec.return_value = self._create_mock_spec_result()
                                mock_code.return_value = self._create_mock_code_result()
                                mock_execute.return_value = self._create_mock_execution_result(success=True)

                                # Execute orchestration
                                result = orchestrator.orchestrate_task(
                                    image_path=self.test_image_path,
                                    user_preferences=user_preferences,
                                    model_name=model_name,
                                    description=description
                                )

                                # Verify result
                                self.assertIsNotNone(result.task_id)
                                self.assertTrue(result.success)

            print("✅ Task context creation working correctly")
    
    def test_workflow_stages(self):
        """Test workflow stage progression."""
        print("\n--- Testing Workflow Stages ---")

        # Mock BlenderExecutor to avoid Blender dependency
        with patch('main_orchestrator.BlenderExecutor') as mock_blender:
            mock_blender.return_value = Mock()

            orchestrator = OrchestratorAgent(
                config=self.config,
                workspace_dir=self.test_dir
            )
        
            # Mock all agents for controlled testing
            with patch.object(orchestrator.image_handler, 'process_image') as mock_process:
                with patch.object(orchestrator.image_analysis_agent, 'analyze_image') as mock_analyze:
                    with patch.object(orchestrator.spec_generation_agent, 'generate_specification') as mock_spec:
                        with patch.object(orchestrator.code_generation_agent, 'generate_blender_code') as mock_code:
                            with patch.object(orchestrator.blender_executor, 'execute_script') as mock_execute:

                                # Setup successful mocks
                                mock_process.return_value = Mock(processed_path="processed.png")
                                mock_analyze.return_value = self._create_mock_analysis_result()
                                mock_spec.return_value = self._create_mock_spec_result()
                                mock_code.return_value = self._create_mock_code_result()
                                mock_execute.return_value = self._create_mock_execution_result(success=True)

                                # Execute orchestration
                                result = orchestrator.orchestrate_task(
                                    image_path=self.test_image_path,
                                    user_preferences={"units": "meters"}
                                )

                                # Verify stages were completed
                                expected_stages = [
                                    WorkflowStage.IMAGE_PROCESSING,
                                    WorkflowStage.IMAGE_ANALYSIS,
                                    WorkflowStage.SPEC_GENERATION,
                                    WorkflowStage.CODE_GENERATION,
                                    WorkflowStage.BLENDER_EXECUTION
                                ]

                                for stage in expected_stages:
                                    self.assertIn(stage, result.stages_completed)

                                self.assertTrue(result.success)

            print("✅ Workflow stages progression working correctly")
    
    def test_inner_loop_error_correction(self):
        """Test inner loop error correction mechanism."""
        print("\n--- Testing Inner Loop Error Correction ---")

        # Mock BlenderExecutor to avoid Blender dependency
        with patch('main_orchestrator.BlenderExecutor') as mock_blender:
            mock_blender.return_value = Mock()

            orchestrator = OrchestratorAgent(
                config=self.config,
                workspace_dir=self.test_dir
            )

            with patch.object(orchestrator.image_handler, 'process_image') as mock_process:
                with patch.object(orchestrator.image_analysis_agent, 'analyze_image') as mock_analyze:
                    with patch.object(orchestrator.spec_generation_agent, 'generate_specification') as mock_spec:
                        with patch.object(orchestrator.code_generation_agent, 'generate_blender_code') as mock_code:
                            with patch.object(orchestrator.blender_executor, 'execute_script') as mock_execute:
                                with patch.object(orchestrator.validator_debugger_agent, 'validate_and_debug') as mock_debug:

                                    # Setup mocks
                                    mock_process.return_value = Mock(processed_path="processed.png")
                                    mock_analyze.return_value = self._create_mock_analysis_result()
                                    mock_spec.return_value = self._create_mock_spec_result()
                                    mock_code.return_value = self._create_mock_code_result()

                                    # First execution fails, second succeeds
                                    mock_execute.side_effect = [
                                        self._create_mock_execution_result(success=False),
                                        self._create_mock_execution_result(success=True)
                                    ]

                                    # Mock debugger fixes the code
                                    mock_debug.return_value = self._create_mock_validation_result()

                                    # Execute orchestration
                                    result = orchestrator.orchestrate_task(
                                        image_path=self.test_image_path,
                                        user_preferences={"units": "meters"}
                                    )

                                    # Verify inner loop was executed
                                    self.assertTrue(result.success)
                                    self.assertEqual(result.inner_loop_iterations, 2)
                                    self.assertIn(WorkflowStage.VALIDATION_DEBUG, result.stages_completed)

            print("✅ Inner loop error correction working correctly")
    
    def test_task_monitoring(self):
        """Test task monitoring capabilities."""
        print("\n--- Testing Task Monitoring ---")

        # Mock BlenderExecutor to avoid Blender dependency
        with patch('main_orchestrator.BlenderExecutor') as mock_blender:
            mock_blender.return_value = Mock()

            orchestrator = OrchestratorAgent(
                config=self.config,
                workspace_dir=self.test_dir
            )

            # Initially no active tasks
            active_tasks = orchestrator.list_active_tasks()
            self.assertEqual(len(active_tasks), 0)

            # Test task status for non-existent task
            status = orchestrator.get_task_status("non_existent_task")
            self.assertIsNone(status)

            # Test task cancellation for non-existent task
            cancelled = orchestrator.cancel_task("non_existent_task")
            self.assertFalse(cancelled)

            print("✅ Task monitoring working correctly")
    
    def _create_mock_analysis_result(self):
        """Create a mock image analysis result."""
        bbox = BoundingBox(x=0.25, y=0.25, width=0.5, height=0.5)
        color = ColorInfo(r=1.0, g=0.0, b=0.0, dominant_color_name="red")
        shape = DetectedShape(
            shape_type=ShapeType.CUBE,
            confidence=0.9,
            bounding_box=bbox,
            color_info=color
        )
        
        return ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=[shape],
            overall_confidence=0.9,
            analysis_granularity=AnalysisGranularity.BASIC,
            scene_description="A red cube"
        )
    
    def _create_mock_spec_result(self):
        """Create a mock specification generation result."""
        spec = {
            "schema_version": "2.0.0",
            "model_info": {"name": "Test Model", "description": "Test"},
            "scene_settings": {"units": "meters"},
            "objects": [{"name": "cube", "geometry": {"type": "cube"}}]
        }

        return SpecGenerationResult(
            specification=spec,
            validation_passed=True,
            validation_errors=[],
            knowledge_context_used=["test_context"],
            generation_time=1.0,
            confidence_score=0.9
        )
    
    def _create_mock_code_result(self):
        """Create a mock code generation result."""
        from agents.code_generation_agent import CodeAnalysisResult, CodeQuality

        analysis_result = CodeAnalysisResult(
            syntax_valid=True,
            ast_parseable=True,
            quality_score=0.9,
            quality_level=CodeQuality.GOOD,
            issues=[],
            suggestions=[],
            complexity_score=5,
            line_count=2
        )

        return CodeGenerationResult(
            generated_code="import bpy\nbpy.ops.mesh.primitive_cube_add()",
            specification_used={"test": "spec"},
            analysis_result=analysis_result,
            knowledge_context_used=["test_context"],
            generation_time=1.0,
            confidence_score=0.9
        )
    
    def _create_mock_execution_result(self, success=True):
        """Create a mock Blender execution result."""
        if success:
            return BlenderOutput(
                status=BlenderExecutionStatus.SUCCESS,
                stdout="Blender execution completed",
                stderr="",
                return_code=0,
                execution_time=2.0,
                output_files=["output.blend"]
            )
        else:
            return BlenderOutput(
                status=BlenderExecutionStatus.ERROR,
                stdout="",
                stderr="Mock execution error",
                return_code=1,
                execution_time=1.0,
                output_files=[]
            )
    
    def _create_mock_validation_result(self):
        """Create a mock validation result."""
        return ValidationResult(
            is_valid=True,
            diagnosis=None,
            fixed_code="import bpy\nbpy.ops.mesh.primitive_cube_add(location=(0,0,0))",
            fix_applied=True,
            validation_time=1.0,
            retry_count=1
        )


def run_orchestrator_tests():
    """Run orchestrator integration tests."""
    print("=== Running Orchestrator Integration Tests ===")
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add tests
    suite.addTest(TestOrchestratorIntegration('test_orchestrator_initialization'))
    suite.addTest(TestOrchestratorIntegration('test_configuration_management'))
    suite.addTest(TestOrchestratorIntegration('test_task_context_creation'))
    suite.addTest(TestOrchestratorIntegration('test_workflow_stages'))
    suite.addTest(TestOrchestratorIntegration('test_inner_loop_error_correction'))
    suite.addTest(TestOrchestratorIntegration('test_task_monitoring'))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=0)
    result = runner.run(suite)
    
    return result


if __name__ == "__main__":
    result = run_orchestrator_tests()
    
    print(f"\n=== Test Summary ===")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\nSuccess rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("✅ Orchestrator integration tests PASSED!")
        print("\n🎉 Task 5.1 implementation is working correctly!")
    else:
        print("❌ Some tests failed. Please review the implementation.")

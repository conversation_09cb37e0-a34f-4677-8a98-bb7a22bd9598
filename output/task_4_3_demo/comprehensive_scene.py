import bpy
import bmesh
from mathutils import Vector, Euler
import math

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Clear existing materials
for material in bpy.data.materials:
    bpy.data.materials.remove(material)

# Clear existing node groups (for MCP)
for node_group in bpy.data.node_groups:
    bpy.data.node_groups.remove(node_group)

# Generated model: Comprehensive V2 Scene
# Description: A scene showcasing all v2.0.0 features
# Generated at: 2025-07-19T14:49:16.622539


# Create cube: Hero Object
bpy.ops.mesh.primitive_cube_add(
    size=2.0,
    location=(0.0, 0.0, 0.0)
)
cube_obj = bpy.context.active_object
cube_obj.name = "Hero Object"
cube_obj.rotation_euler = (0.0, 0.0, 0.0)
cube_obj.scale = (1.0, 1.0, 1.0)

# Create principled material: hero_material
material = bpy.data.materials.new(name="hero_material")
material.use_nodes = True
bsdf = material.node_tree.nodes["Principled BSDF"]
bsdf.inputs[0].default_value = (0.8, 0.2, 0.1, 1.0)  # Base Color
bsdf.inputs[1].default_value = 0.7  # Metallic
bsdf.inputs[2].default_value = 0.3  # Roughness
bsdf.inputs[17].default_value = 0.1  # Transmission
bsdf.inputs[18].default_value = 1.5  # IOR
bsdf.inputs[19].default_value = (0.1, 0.05, 0.0, 1.0)  # Emission
bsdf.inputs[20].default_value = 1.5  # Emission Strength
cube_obj.data.materials.append(material)

# Add bevel modifier: hero_bevel
bevel_modifier = cube_obj.modifiers.new(name="hero_bevel", type='BEVEL')
bevel_modifier.width = 0.1
bevel_modifier.segments = 3

# Add subdivision surface modifier: hero_smooth
subsurf_modifier = cube_obj.modifiers.new(name="hero_smooth", type='SUBSURF')
subsurf_modifier.levels = 2
subsurf_modifier.render_levels = 2


# Create molecular structure: Demo Molecule
# Note: This requires Molecular Nodes addon to be enabled
try:
    import molecular_nodes as mn
    mol_obj = mn.create_molecule(name="Demo Molecule")
    mol_obj.location = (5.0, 0.0, 0.0)
    mol_obj.rotation_euler = (0.0, 0.0, 0.0)
    mol_obj.scale = (1.0, 1.0, 1.0)
except ImportError:
    print("Warning: Molecular Nodes addon not available, creating placeholder cube")
    bpy.ops.mesh.primitive_cube_add(location=(5.0, 0.0, 0.0))
    placeholder = bpy.context.active_object
    placeholder.name = "Demo Molecule_placeholder"

# Animation for Hero Object

# Set keyframe for location at frame 1
bpy.context.scene.frame_set(1)
obj = bpy.data.objects["Hero Object"]
obj.location = (0.0, 0.0, 0.0)
obj.keyframe_insert(data_path="location", frame=1)

# Set keyframe for rotation at frame 60
bpy.context.scene.frame_set(60)
obj = bpy.data.objects["Hero Object"]
obj.rotation = (0.0, 6.28, 0.0)
obj.keyframe_insert(data_path="rotation", frame=60)

# Set keyframe for scale at frame 120
bpy.context.scene.frame_set(120)
obj = bpy.data.objects["Hero Object"]
obj.scale = (1.5, 1.5, 1.5)
obj.keyframe_insert(data_path="scale", frame=120)

# Update scene
bpy.context.view_layer.update()

# Optional: Save the file
# bpy.ops.wm.save_as_mainfile(filepath="/path/to/output.blend")


# Error handling refinement
try:
    bpy.ops.object.select_all(action='DESELECT')
except:
    pass


# Error handling refinement
try:
    bpy.ops.object.select_all(action='DESELECT')
except:
    pass

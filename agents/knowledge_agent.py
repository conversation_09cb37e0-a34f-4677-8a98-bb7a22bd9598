"""
Knowledge Agent for Blender 3D Model Generation AI Agent System - Enhanced Version

This module implements an advanced knowledge retrieval agent that provides access to Blender Python API
documentation, MCP (Molecular Nodes) usage examples, and 3D modeling best practices through
a vector database-powered RAG (Retrieval-Augmented Generation) system.

Enhanced features for Task 4.4:
- Hybrid retrieval (keyword + vector search)
- Reranking mechanisms for improved accuracy
- Performance optimizations with caching
- Expanded knowledge base support
- Advanced query processing

Enhanced with Ray RLlib integration for optimal tool selection and decision making.

Author: Augment Agent
Date: 2025-07-19
Version: 2.0.0 - Task 4.4 Enhanced
"""

import os
import logging
import time
import hashlib
import json
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, Counter
import threading
from concurrent.futures import ThreadPoolExecutor

import chromadb
from chromadb.config import Settings
import openai
from openai import OpenAI

# Ray RLlib imports (optional)
try:
    import ray
    import numpy as np
    from ray.rllib.algorithms.ppo import PPOConfig
    from ray.rllib.env.env_context import EnvContext
    HAS_RAY = True
except ImportError:
    HAS_RAY = False
    print("Warning: Ray RLlib not available. Using fallback tool selection.")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeToolType(Enum):
    """Enumeration of available knowledge tools for RL selection."""
    VECTOR_SEARCH = 0
    KEYWORD_SEARCH = 1
    HYBRID_SEARCH = 2  # New: Combined keyword + vector
    BLENDER_API_LOOKUP = 3
    MCP_EXAMPLES = 4
    BEST_PRACTICES = 5
    CODE_EXAMPLES = 6  # New: Code examples search
    RERANKED_SEARCH = 7  # New: Reranked results


@dataclass
class ToolSelectionContext:
    """Context for RL-based tool selection."""
    query_type: str
    query_complexity: float
    domain_specificity: float
    previous_tool_success: float
    time_constraint: float
    query_intent: Optional[str] = None  # New: Intent classification
    user_expertise_level: Optional[str] = None  # New: User expertise


@dataclass
class QueryAnalysis:
    """Analysis of user query for optimal retrieval strategy."""
    keywords: List[str]
    intent: str  # 'api_lookup', 'example_request', 'best_practice', 'troubleshooting'
    complexity_score: float
    domain_specificity: float
    requires_code: bool
    language_patterns: List[str]


@dataclass
class CacheEntry:
    """Cache entry for query results."""
    query_hash: str
    results: List['RetrievalResult']
    timestamp: float
    access_count: int
    tool_used: KnowledgeToolType


class KnowledgeSource(Enum):
    """Enumeration of knowledge source types."""
    BLENDER_API = "blender_api"
    MCP_DOCS = "mcp_docs"
    BEST_PRACTICES = "best_practices"
    CODE_EXAMPLES = "code_examples"  # New: Code examples
    EXAMPLES = "examples"


@dataclass
class KnowledgeChunk:
    """Represents a chunk of knowledge with metadata."""
    id: str
    content: str
    source: KnowledgeSource
    topic: str
    subtopic: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    keywords: Optional[List[str]] = None  # New: Extracted keywords
    code_snippets: Optional[List[str]] = None  # New: Code snippets
    difficulty_level: Optional[str] = None  # New: Difficulty level


@dataclass
class RetrievalResult:
    """Represents a knowledge retrieval result."""
    chunk: KnowledgeChunk
    relevance_score: float
    distance: float
    keyword_match_score: Optional[float] = None  # New: Keyword matching score
    rerank_score: Optional[float] = None  # New: Reranking score
    retrieval_method: Optional[str] = None  # New: Method used for retrieval


class KnowledgeRetrievalError(Exception):
    """Exception raised for knowledge retrieval errors."""
    pass


class KnowledgeAgent:
    """
    Knowledge Agent for retrieving Blender API documentation and MCP examples.
    
    This agent uses ChromaDB for vector storage and OpenAI embeddings for semantic search.
    It provides context-aware knowledge retrieval to support other agents in the system.
    """
    
    def __init__(self,
                 knowledge_base_path: str = "knowledge_base",
                 db_path: str = "chroma_db",
                 openai_api_key: Optional[str] = None,
                 embedding_model: str = "text-embedding-3-small",
                 enable_rl: bool = True,
                 cache_size: int = 1000,
                 enable_hybrid_search: bool = True):
        """
        Initialize the Enhanced Knowledge Agent.

        Args:
            knowledge_base_path: Path to knowledge base files
            db_path: Path to ChromaDB storage
            openai_api_key: OpenAI API key for embeddings
            embedding_model: OpenAI embedding model to use
            enable_rl: Whether to enable RL-based tool selection
            cache_size: Maximum number of cached query results
            enable_hybrid_search: Whether to enable hybrid search capabilities
        """
        self.knowledge_base_path = Path(knowledge_base_path)
        self.db_path = Path(db_path)
        self.embedding_model = embedding_model
        self.enable_rl = enable_rl and HAS_RAY
        self.enable_hybrid_search = enable_hybrid_search

        # Initialize OpenAI client
        api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            logger.warning("No OpenAI API key provided. Some functionality may be limited.")
            self.openai_client = None
        else:
            self.openai_client = OpenAI(api_key=api_key)

        # Initialize ChromaDB
        self.chroma_client = chromadb.PersistentClient(
            path=str(self.db_path),
            settings=Settings(anonymized_telemetry=False)
        )

        # Create or get collection
        self.collection = self.chroma_client.get_or_create_collection(
            name="blender_knowledge",
            metadata={"description": "Blender API and MCP knowledge base"}
        )

        self.knowledge_chunks: List[KnowledgeChunk] = []

        # Enhanced features for Task 4.4
        self.query_cache: Dict[str, CacheEntry] = {}
        self.cache_size = cache_size
        self.cache_lock = threading.Lock()

        # Performance tracking
        self.retrieval_stats = {
            'total_queries': 0,
            'cache_hits': 0,
            'avg_retrieval_time': 0.0,
            'method_usage': defaultdict(int)
        }

        # Keyword extraction patterns
        self.api_patterns = [
            r'bpy\.[a-zA-Z_][a-zA-Z0-9_.]*',
            r'ops\.[a-zA-Z_][a-zA-Z0-9_.]*',
            r'data\.[a-zA-Z_][a-zA-Z0-9_.]*'
        ]

        # Initialize RL components
        self._init_rl_components()

        # Tool performance tracking for reward calculation
        self.tool_performance_history = {tool: [] for tool in KnowledgeToolType}
        self.query_success_rate = 0.0

    def _init_rl_components(self):
        """Initialize RL components for tool selection."""
        if not self.enable_rl:
            logger.info("RL disabled, using heuristic tool selection")
            self.rl_policy = None
            return

        try:
            # Initialize Ray if not already done
            if not ray.is_initialized():
                ray.init(ignore_reinit_error=True)

            # Create simple RL environment for knowledge tool selection
            from rl_env.minimal_blender_task_env import MinimalBlenderTaskEnv

            # Configure PPO for knowledge tool selection
            self.rl_config = (
                PPOConfig()
                .environment(MinimalBlenderTaskEnv)
                .framework("torch")
                .training(
                    lr=0.0001,
                    num_sgd_iter=5,
                    train_batch_size=500,
                )
                .resources(num_gpus=0)
            )

            # Build algorithm (will use pre-trained if available)
            self.rl_policy = self.rl_config.build()
            logger.info("RL policy initialized for knowledge tool selection")

        except Exception as e:
            logger.warning(f"Failed to initialize RL components: {e}")
            self.enable_rl = False
            self.rl_policy = None

    def load_knowledge_base(self, force_reload: bool = False) -> bool:
        """
        Load knowledge base from files and create embeddings.
        
        Args:
            force_reload: Force reload even if collection already has data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if collection already has data
            if not force_reload and self.collection.count() > 0:
                logger.info(f"Knowledge base already loaded with {self.collection.count()} chunks")
                return True
            
            # Clear existing data if force reload
            if force_reload:
                self.chroma_client.delete_collection("blender_knowledge")
                self.collection = self.chroma_client.create_collection(
                    name="blender_knowledge",
                    metadata={"description": "Blender API and MCP knowledge base"}
                )
            
            # Load knowledge chunks from files
            self._load_knowledge_chunks()
            
            if not self.knowledge_chunks:
                logger.warning("No knowledge chunks loaded")
                return False
            
            # Generate embeddings and store in vector database
            self._store_embeddings()
            
            logger.info(f"Successfully loaded {len(self.knowledge_chunks)} knowledge chunks")
            return True

        except Exception as e:
            logger.error(f"Failed to load knowledge base: {e}")
            raise KnowledgeRetrievalError(f"Knowledge base loading failed: {e}")

    def analyze_query(self, query: str) -> QueryAnalysis:
        """Analyze query to determine optimal retrieval strategy."""
        # Extract keywords
        keywords = self._extract_keywords(query)

        # Classify intent
        intent = self._classify_intent(query)

        # Calculate complexity score
        complexity_score = self._calculate_complexity_score(query)

        # Calculate domain specificity
        domain_specificity = self._calculate_domain_specificity(query, keywords)

        # Check if code is required
        requires_code = self._requires_code_examples(query)

        # Extract language patterns
        language_patterns = self._extract_language_patterns(query)

        return QueryAnalysis(
            keywords=keywords,
            intent=intent,
            complexity_score=complexity_score,
            domain_specificity=domain_specificity,
            requires_code=requires_code,
            language_patterns=language_patterns
        )

    def _extract_keywords(self, query: str) -> List[str]:
        """Extract relevant keywords from query."""
        keywords = []

        # Extract API patterns
        for pattern in self.api_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            keywords.extend(matches)

        # Extract common 3D modeling terms
        modeling_terms = [
            'mesh', 'vertex', 'edge', 'face', 'material', 'texture', 'shader',
            'modifier', 'animation', 'keyframe', 'bone', 'armature', 'camera',
            'light', 'render', 'scene', 'object', 'collection', 'layer'
        ]

        query_lower = query.lower()
        for term in modeling_terms:
            if term in query_lower:
                keywords.append(term)

        # Extract molecular terms
        molecular_terms = [
            'atom', 'bond', 'molecule', 'protein', 'dna', 'rna', 'molecular',
            'chemical', 'structure', 'pdb', 'sdf', 'mol', 'chain', 'residue'
        ]

        for term in molecular_terms:
            if term in query_lower:
                keywords.append(term)

        return list(set(keywords))  # Remove duplicates

    def _classify_intent(self, query: str) -> str:
        """Classify the intent of the query."""
        query_lower = query.lower()

        # API lookup patterns
        if any(pattern in query_lower for pattern in ['how to', 'api', 'function', 'method', 'bpy.']):
            return 'api_lookup'

        # Example request patterns
        if any(pattern in query_lower for pattern in ['example', 'sample', 'demo', 'show me']):
            return 'example_request'

        # Best practice patterns
        if any(pattern in query_lower for pattern in ['best practice', 'optimize', 'efficient', 'performance']):
            return 'best_practice'

        # Troubleshooting patterns
        if any(pattern in query_lower for pattern in ['error', 'problem', 'fix', 'debug', 'issue']):
            return 'troubleshooting'

        return 'general'

    def _calculate_complexity_score(self, query: str) -> float:
        """Calculate complexity score based on query characteristics."""
        score = 0.0

        # Length factor
        score += min(len(query.split()) / 20.0, 0.3)

        # Technical terms factor
        technical_terms = ['modifier', 'constraint', 'animation', 'shader', 'node', 'api']
        tech_count = sum(1 for term in technical_terms if term in query.lower())
        score += min(tech_count / len(technical_terms), 0.3)

        # Code patterns factor
        code_patterns = ['import', 'def ', 'class ', 'for ', 'if ', 'bpy.']
        code_count = sum(1 for pattern in code_patterns if pattern in query.lower())
        score += min(code_count / len(code_patterns), 0.4)

        return min(score, 1.0)

    def _calculate_domain_specificity(self, query: str, keywords: List[str]) -> float:
        """Calculate domain specificity score."""
        if not keywords:
            return 0.0

        # Blender-specific terms
        blender_terms = ['bpy', 'ops', 'data', 'context', 'mesh', 'object']
        blender_count = sum(1 for kw in keywords if any(term in kw.lower() for term in blender_terms))

        # MCP-specific terms
        mcp_terms = ['molecular', 'atom', 'bond', 'protein', 'pdb']
        mcp_count = sum(1 for kw in keywords if any(term in kw.lower() for term in mcp_terms))

        specificity = (blender_count + mcp_count) / len(keywords)
        return min(specificity, 1.0)

    def _requires_code_examples(self, query: str) -> bool:
        """Determine if query requires code examples."""
        code_indicators = [
            'code', 'script', 'python', 'example', 'how to', 'implement',
            'create', 'generate', 'write', 'function', 'method'
        ]

        query_lower = query.lower()
        return any(indicator in query_lower for indicator in code_indicators)

    def _extract_language_patterns(self, query: str) -> List[str]:
        """Extract programming language patterns from query."""
        patterns = []

        # Python patterns
        if re.search(r'import\s+\w+|def\s+\w+|class\s+\w+', query):
            patterns.append('python')

        # Blender API patterns
        if re.search(r'bpy\.|ops\.|data\.', query):
            patterns.append('blender_api')

        return patterns

    def _get_cache_key(self, query: str, tool: KnowledgeToolType, top_k: int) -> str:
        """Generate cache key for query."""
        content = f"{query}_{tool.value}_{top_k}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_results(self, cache_key: str) -> Optional[List[RetrievalResult]]:
        """Get cached results if available and not expired."""
        with self.cache_lock:
            if cache_key in self.query_cache:
                entry = self.query_cache[cache_key]
                # Check if cache entry is still valid (24 hours)
                if time.time() - entry.timestamp < 86400:
                    entry.access_count += 1
                    self.retrieval_stats['cache_hits'] += 1
                    return entry.results
                else:
                    # Remove expired entry
                    del self.query_cache[cache_key]
        return None

    def _cache_results(self, cache_key: str, results: List[RetrievalResult], tool: KnowledgeToolType):
        """Cache query results."""
        with self.cache_lock:
            # Implement LRU cache eviction
            if len(self.query_cache) >= self.cache_size:
                # Remove least recently used entry
                oldest_key = min(self.query_cache.keys(),
                               key=lambda k: self.query_cache[k].timestamp)
                del self.query_cache[oldest_key]

            self.query_cache[cache_key] = CacheEntry(
                query_hash=cache_key,
                results=results,
                timestamp=time.time(),
                access_count=1,
                tool_used=tool
            )

    def _load_knowledge_chunks(self):
        """Load knowledge chunks from text files."""
        self.knowledge_chunks = []
        
        # Load from blender_docs_subset.txt
        blender_docs_path = self.knowledge_base_path / "blender_docs_subset.txt"
        if blender_docs_path.exists():
            self._parse_knowledge_file(blender_docs_path, KnowledgeSource.BLENDER_API)
        else:
            logger.warning(f"Blender docs file not found: {blender_docs_path}")
        
        # Load additional knowledge files if they exist
        file_mappings = {
            KnowledgeSource.MCP_DOCS: "mcp_docs.txt",
            KnowledgeSource.BEST_PRACTICES: "best_practices_docs.txt",
            KnowledgeSource.CODE_EXAMPLES: "code_examples_docs.txt",
            KnowledgeSource.EXAMPLES: "examples_docs.txt"
        }

        for source_type, filename in file_mappings.items():
            file_path = self.knowledge_base_path / filename
            if file_path.exists():
                self._parse_knowledge_file(file_path, source_type)
            else:
                logger.debug(f"Optional knowledge file not found: {file_path}")
    
    def _parse_knowledge_file(self, file_path: Path, source: KnowledgeSource):
        """Parse a knowledge file and extract chunks."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Split content into chunks (simple approach - can be enhanced)
            chunks = self._split_into_chunks(content)
            
            for i, chunk_content in enumerate(chunks):
                if chunk_content.strip():
                    chunk_id = f"{source.value}_{file_path.stem}_{i}"
                    topic = self._extract_topic(chunk_content)
                    
                    chunk = KnowledgeChunk(
                        id=chunk_id,
                        content=chunk_content.strip(),
                        source=source,
                        topic=topic,
                        metadata={"file": str(file_path)}
                    )
                    self.knowledge_chunks.append(chunk)
                    
        except Exception as e:
            logger.error(f"Failed to parse knowledge file {file_path}: {e}")
    
    def _split_into_chunks(self, content: str, max_chunk_size: int = 1000) -> List[str]:
        """Split content into manageable chunks."""
        # Simple chunking by paragraphs and size
        paragraphs = content.split('\n\n')
        chunks = []
        current_chunk = ""

        for paragraph in paragraphs:
            # If paragraph itself is too long, split it further
            if len(paragraph) > max_chunk_size:
                # Add current chunk if it exists
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = ""

                # Split long paragraph by sentences or words
                sentences = paragraph.split('. ')
                for sentence in sentences:
                    if len(sentence) > max_chunk_size:
                        # Split by words if sentence is still too long
                        words = sentence.split()
                        temp_chunk = ""
                        for word in words:
                            if len(temp_chunk) + len(word) + 1 > max_chunk_size and temp_chunk:
                                chunks.append(temp_chunk)
                                temp_chunk = word
                            else:
                                temp_chunk += " " + word if temp_chunk else word
                        if temp_chunk:
                            chunks.append(temp_chunk)
                    else:
                        if len(current_chunk) + len(sentence) > max_chunk_size and current_chunk:
                            chunks.append(current_chunk)
                            current_chunk = sentence
                        else:
                            current_chunk += ". " + sentence if current_chunk else sentence
            else:
                # Normal paragraph processing
                if len(current_chunk) + len(paragraph) > max_chunk_size and current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = paragraph
                else:
                    current_chunk += "\n\n" + paragraph if current_chunk else paragraph

        if current_chunk:
            chunks.append(current_chunk)

        return chunks
    
    def _extract_topic(self, content: str) -> str:
        """Extract topic from content (simple heuristic)."""
        lines = content.strip().split('\n')
        first_line = lines[0].strip()
        
        # Look for common patterns
        if first_line.startswith('#'):
            return first_line.strip('#').strip()
        elif 'bpy.' in first_line:
            return first_line.split('bpy.')[1].split('(')[0] if '(' in first_line else first_line.split('bpy.')[1]
        elif len(first_line) < 100:
            return first_line
        else:
            return "general"
    
    def _store_embeddings(self):
        """Generate embeddings and store in ChromaDB."""
        if not self.openai_client:
            logger.warning("No OpenAI client available for embeddings")
            # Store without embeddings for testing
            self._store_without_embeddings()
            return
        
        batch_size = 100
        for i in range(0, len(self.knowledge_chunks), batch_size):
            batch = self.knowledge_chunks[i:i + batch_size]
            self._process_embedding_batch(batch)
    
    def _process_embedding_batch(self, batch: List[KnowledgeChunk]):
        """Process a batch of chunks for embedding."""
        try:
            # Prepare texts for embedding
            texts = [chunk.content for chunk in batch]
            
            # Generate embeddings
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=texts
            )
            
            # Prepare data for ChromaDB
            ids = [chunk.id for chunk in batch]
            embeddings = [data.embedding for data in response.data]
            documents = texts
            metadatas = [
                {
                    "source": chunk.source.value,
                    "topic": chunk.topic,
                    "subtopic": chunk.subtopic or "",
                    **(chunk.metadata or {})
                }
                for chunk in batch
            ]
            
            # Add to collection
            self.collection.add(
                ids=ids,
                embeddings=embeddings,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info(f"Processed embedding batch of {len(batch)} chunks")
            
        except Exception as e:
            logger.error(f"Failed to process embedding batch: {e}")
            raise KnowledgeRetrievalError(f"Embedding generation failed: {e}")
    
    def _store_without_embeddings(self):
        """Store chunks without embeddings (for testing)."""
        ids = [chunk.id for chunk in self.knowledge_chunks]
        documents = [chunk.content for chunk in self.knowledge_chunks]
        metadatas = [
            {
                "source": chunk.source.value,
                "topic": chunk.topic,
                "subtopic": chunk.subtopic or "",
                **(chunk.metadata or {})
            }
            for chunk in self.knowledge_chunks
        ]
        
        self.collection.add(
            ids=ids,
            documents=documents,
            metadatas=metadatas
        )

    def _select_optimal_tool(self, context: ToolSelectionContext) -> KnowledgeToolType:
        """
        Select optimal knowledge tool using RL policy or heuristics.

        Args:
            context: Context for tool selection

        Returns:
            Selected knowledge tool type
        """
        if self.enable_rl and self.rl_policy:
            try:
                # Create state vector for RL policy
                state = np.array([
                    hash(context.query_type) % 10 / 10.0,  # Normalized query type
                    context.query_complexity,
                    context.domain_specificity,
                    context.previous_tool_success,
                    context.time_constraint
                ], dtype=np.float32)

                # Get action from RL policy
                action = self.rl_policy.compute_single_action(state)

                # Map action to tool type
                if action < len(KnowledgeToolType):
                    selected_tool = list(KnowledgeToolType)[action]
                    logger.debug(f"RL selected tool: {selected_tool.name}")
                    return selected_tool

            except Exception as e:
                logger.warning(f"RL tool selection failed: {e}, falling back to heuristics")

        # Fallback to heuristic selection
        return self._heuristic_tool_selection(context)

    def _heuristic_tool_selection(self, context: ToolSelectionContext) -> KnowledgeToolType:
        """Fallback heuristic tool selection."""
        # Simple heuristics based on query characteristics
        if "blender" in context.query_type.lower() and "api" in context.query_type.lower():
            return KnowledgeToolType.BLENDER_API_LOOKUP
        elif "molecular" in context.query_type.lower() or "mcp" in context.query_type.lower():
            return KnowledgeToolType.MCP_EXAMPLES
        elif context.query_complexity > 0.7:
            return KnowledgeToolType.VECTOR_SEARCH
        elif context.domain_specificity < 0.3:
            return KnowledgeToolType.BEST_PRACTICES
        else:
            return KnowledgeToolType.KEYWORD_SEARCH

    def _calculate_tool_reward(self, tool: KnowledgeToolType,
                              query_success: bool,
                              relevance_score: float,
                              response_time: float) -> float:
        """
        Calculate reward for tool selection to update RL policy.

        Args:
            tool: Tool that was used
            query_success: Whether query was successful
            relevance_score: Average relevance score of results
            response_time: Time taken to respond

        Returns:
            Calculated reward value
        """
        # Base reward for success/failure
        base_reward = 5.0 if query_success else -2.0

        # Quality bonus based on relevance
        quality_bonus = relevance_score * 3.0

        # Efficiency bonus (faster is better)
        efficiency_bonus = max(0, 2.0 - response_time)

        # Tool-specific bonuses
        tool_bonus = 0.0
        if tool == KnowledgeToolType.VECTOR_SEARCH and relevance_score > 0.8:
            tool_bonus = 1.0
        elif tool == KnowledgeToolType.BLENDER_API_LOOKUP and "blender" in str(query_success):
            tool_bonus = 1.5

        total_reward = base_reward + quality_bonus + efficiency_bonus + tool_bonus

        # Update performance history
        self.tool_performance_history[tool].append(total_reward)
        if len(self.tool_performance_history[tool]) > 100:
            self.tool_performance_history[tool].pop(0)  # Keep last 100 records

        return total_reward

    def query_knowledge(self,
                       query: str,
                       top_k: int = 5,
                       source_filter: Optional[KnowledgeSource] = None,
                       enable_rl_selection: bool = True,
                       enable_reranking: bool = True) -> List[RetrievalResult]:
        """
        Enhanced query method with caching, analysis, and hybrid retrieval.

        Args:
            query: Search query
            top_k: Number of top results to return
            source_filter: Filter by knowledge source type
            enable_rl_selection: Whether to use RL for tool selection
            enable_reranking: Whether to apply reranking to results

        Returns:
            List of retrieval results sorted by relevance
        """
        start_time = time.time()
        self.retrieval_stats['total_queries'] += 1

        try:
            if self.collection.count() == 0:
                logger.warning("Knowledge base is empty. Load knowledge base first.")
                return []

            # Analyze query for optimal strategy
            query_analysis = self.analyze_query(query)

            # Enhanced context for RL tool selection
            if enable_rl_selection and self.enable_rl:
                context = ToolSelectionContext(
                    query_type=query,
                    query_complexity=query_analysis.complexity_score,
                    domain_specificity=query_analysis.domain_specificity,
                    previous_tool_success=self.query_success_rate,
                    time_constraint=0.5,  # Default medium urgency
                    query_intent=query_analysis.intent,
                    user_expertise_level="intermediate"  # Default
                )

                selected_tool = self._select_optimal_tool(context)
                logger.debug(f"Selected tool for query '{query[:50]}...': {selected_tool.name}")
            else:
                # Use query analysis to select tool
                selected_tool = self._select_tool_by_analysis(query_analysis)

            # Check cache first
            cache_key = self._get_cache_key(query, selected_tool, top_k)
            cached_results = self._get_cached_results(cache_key)
            if cached_results:
                logger.debug(f"Cache hit for query: {query[:50]}...")
                return cached_results

            # Execute query based on selected tool
            results = self._execute_tool_query(query, top_k, source_filter, selected_tool)

            # Apply reranking if enabled and beneficial
            if enable_reranking and len(results) > 1:
                results = self._rerank_results(query, results, query_analysis)

            # Cache results
            self._cache_results(cache_key, results, selected_tool)

            # Calculate performance metrics
            response_time = time.time() - start_time
            avg_relevance = sum(r.relevance_score for r in results) / len(results) if results else 0.0
            query_success = len(results) > 0 and avg_relevance > 0.3

            # Update statistics
            self.retrieval_stats['avg_retrieval_time'] = (
                0.9 * self.retrieval_stats['avg_retrieval_time'] + 0.1 * response_time
            )
            self.retrieval_stats['method_usage'][selected_tool.name] += 1

            # Update RL policy with reward
            if enable_rl_selection and self.enable_rl:
                reward = self._calculate_tool_reward(selected_tool, query_success, avg_relevance, response_time)
                logger.debug(f"Tool {selected_tool.name} reward: {reward:.2f}")

            # Update success rate
            self.query_success_rate = 0.9 * self.query_success_rate + 0.1 * (1.0 if query_success else 0.0)

            return results

        except Exception as e:
            logger.error(f"Knowledge query failed: {e}")
            return []

    def _select_tool_by_analysis(self, analysis: QueryAnalysis) -> KnowledgeToolType:
        """Select tool based on query analysis."""
        # Intent-based selection
        if analysis.intent == 'api_lookup':
            return KnowledgeToolType.BLENDER_API_LOOKUP
        elif analysis.intent == 'example_request' and analysis.requires_code:
            return KnowledgeToolType.CODE_EXAMPLES
        elif analysis.intent == 'best_practice':
            return KnowledgeToolType.BEST_PRACTICES
        elif analysis.domain_specificity > 0.7:
            return KnowledgeToolType.MCP_EXAMPLES if 'molecular' in analysis.keywords else KnowledgeToolType.BLENDER_API_LOOKUP
        elif self.enable_hybrid_search and analysis.complexity_score > 0.5:
            return KnowledgeToolType.HYBRID_SEARCH
        else:
            return KnowledgeToolType.VECTOR_SEARCH

    def _execute_tool_query(self, query: str, top_k: int,
                           source_filter: Optional[KnowledgeSource],
                           tool: KnowledgeToolType) -> List[RetrievalResult]:
        """Execute query using the selected tool."""

        # Prepare query filters
        where_filter = {}
        if source_filter:
            where_filter["source"] = source_filter.value

        # Tool-specific query execution
        if tool == KnowledgeToolType.VECTOR_SEARCH and self.openai_client:
            # Generate query embedding
            query_embedding = self._generate_query_embedding(query)

            # Perform vector search
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=where_filter if where_filter else None
            )
        elif tool == KnowledgeToolType.BLENDER_API_LOOKUP:
            # Enhanced query for Blender API
            enhanced_query = f"blender python bpy {query}"
            if self.openai_client:
                query_embedding = self._generate_query_embedding(enhanced_query)
                results = self.collection.query(
                    query_embeddings=[query_embedding],
                    n_results=top_k,
                    where={"source": "BLENDER_DOCS"} if not where_filter else where_filter
                )
            else:
                results = self.collection.query(
                    query_texts=[enhanced_query],
                    n_results=top_k,
                    where={"source": "BLENDER_DOCS"} if not where_filter else where_filter
                )
        elif tool == KnowledgeToolType.MCP_EXAMPLES:
            # MCP-specific search
            mcp_query = f"molecular nodes {query}"
            if self.openai_client:
                query_embedding = self._generate_query_embedding(mcp_query)
                results = self.collection.query(
                    query_embeddings=[query_embedding],
                    n_results=top_k,
                    where={"source": "MCP_DOCS"} if not where_filter else where_filter
                )
            else:
                results = self.collection.query(
                    query_texts=[mcp_query],
                    n_results=top_k,
                    where={"source": "MCP_DOCS"} if not where_filter else where_filter
                )
        elif tool == KnowledgeToolType.CODE_EXAMPLES:
            # Code examples search
            code_query = f"code example {query}"
            if self.openai_client:
                query_embedding = self._generate_query_embedding(code_query)
                results = self.collection.query(
                    query_embeddings=[query_embedding],
                    n_results=top_k,
                    where={"source": "CODE_EXAMPLES"} if not where_filter else where_filter
                )
            else:
                results = self.collection.query(
                    query_texts=[code_query],
                    n_results=top_k,
                    where={"source": "CODE_EXAMPLES"} if not where_filter else where_filter
                )
        elif tool == KnowledgeToolType.BEST_PRACTICES:
            # Best practices search
            bp_query = f"best practice {query}"
            if self.openai_client:
                query_embedding = self._generate_query_embedding(bp_query)
                results = self.collection.query(
                    query_embeddings=[query_embedding],
                    n_results=top_k,
                    where={"source": "BEST_PRACTICES"} if not where_filter else where_filter
                )
            else:
                results = self.collection.query(
                    query_texts=[bp_query],
                    n_results=top_k,
                    where={"source": "BEST_PRACTICES"} if not where_filter else where_filter
                )
        elif tool == KnowledgeToolType.HYBRID_SEARCH:
            # Hybrid search combining vector and keyword search
            return self._hybrid_search(query, top_k, where_filter)
        elif tool == KnowledgeToolType.RERANKED_SEARCH:
            # Get initial results and rerank
            initial_results = self.collection.query(
                query_texts=[query],
                n_results=top_k * 2,  # Get more results for reranking
                where=where_filter if where_filter else None
            )
            # Convert to RetrievalResult and rerank
            retrieval_results = self._convert_to_retrieval_results(initial_results)
            return self._rerank_results(query, retrieval_results, self.analyze_query(query))
        else:
            # Default keyword/text search
            results = self.collection.query(
                query_texts=[query],
                n_results=top_k,
                where=where_filter if where_filter else None
            )

        # Convert to RetrievalResult objects
        retrieval_results = []
        if results and 'ids' in results and results['ids'][0]:
            for i in range(len(results['ids'][0])):
                chunk = KnowledgeChunk(
                    id=results['ids'][0][i],
                    content=results['documents'][0][i],
                    source=KnowledgeSource(results['metadatas'][0][i]['source']),
                    topic=results['metadatas'][0][i]['topic'],
                    subtopic=results['metadatas'][0][i].get('subtopic'),
                    metadata=results['metadatas'][0][i]
                )

                distance = results['distances'][0][i] if 'distances' in results else 0.0
                relevance_score = max(0.0, 1.0 - distance)  # Convert distance to relevance

                retrieval_results.append(RetrievalResult(
                    chunk=chunk,
                    relevance_score=relevance_score,
                    distance=distance
                ))

        logger.debug(f"Tool {tool.name} retrieved {len(retrieval_results)} results")
        return retrieval_results

    def _generate_query_embedding(self, query: str) -> List[float]:
        """Generate embedding for query text."""
        try:
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=query
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Failed to generate query embedding: {e}")
            raise KnowledgeRetrievalError(f"Query embedding failed: {e}")

    def get_blender_api_docs(self, function_name: str) -> List[RetrievalResult]:
        """
        Get Blender API documentation for a specific function.

        Args:
            function_name: Name of the Blender function/API

        Returns:
            List of relevant documentation results
        """
        query = f"bpy.{function_name}" if not function_name.startswith('bpy.') else function_name
        return self.query_knowledge(
            query=query,
            top_k=3,
            source_filter=KnowledgeSource.BLENDER_API
        )

    def get_mcp_examples(self, model_type: str) -> List[RetrievalResult]:
        """
        Get MCP (Molecular Nodes) examples for a specific model type.

        Args:
            model_type: Type of molecular model

        Returns:
            List of relevant MCP examples
        """
        query = f"molecular nodes {model_type}"
        return self.query_knowledge(
            query=query,
            top_k=3,
            source_filter=KnowledgeSource.MCP_DOCS
        )

    def get_knowledge_stats(self) -> Dict[str, Any]:
        """Get statistics about the knowledge base."""
        try:
            total_chunks = self.collection.count()

            # Get source distribution
            source_stats = {}
            for source in KnowledgeSource:
                count = len(self.collection.get(where={"source": source.value})['ids'])
                source_stats[source.value] = count

            return {
                "total_chunks": total_chunks,
                "source_distribution": source_stats,
                "embedding_model": self.embedding_model,
                "has_embeddings": self.openai_client is not None
            }
        except Exception as e:
            logger.error(f"Failed to get knowledge stats: {e}")
            return {"error": str(e)}

    def evaluate_retrieval_quality(self, test_queries: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Evaluate retrieval quality using test queries.

        Args:
            test_queries: List of test queries with expected results

        Returns:
            Dictionary of evaluation metrics
        """
        if not test_queries:
            return {"error": "No test queries provided"}

        total_queries = len(test_queries)
        correct_retrievals = 0
        total_relevance_score = 0.0

        for test_query in test_queries:
            query = test_query['query']
            expected_topics = set(test_query.get('expected_topics', []))

            results = self.query_knowledge(query, top_k=5)

            if results:
                # Check if any result matches expected topics
                retrieved_topics = {result.chunk.topic for result in results}
                if expected_topics.intersection(retrieved_topics):
                    correct_retrievals += 1

                # Calculate average relevance score for top result
                total_relevance_score += results[0].relevance_score

        accuracy = correct_retrievals / total_queries if total_queries > 0 else 0.0
        avg_relevance = total_relevance_score / total_queries if total_queries > 0 else 0.0

        return {
            "accuracy": accuracy,
            "average_relevance_score": avg_relevance,
            "total_queries": total_queries,
            "correct_retrievals": correct_retrievals
        }

    def _hybrid_search(self, query: str, top_k: int, where_filter: Dict) -> List[RetrievalResult]:
        """
        Perform hybrid search combining vector and keyword search.

        Args:
            query: Search query
            top_k: Number of results to return
            where_filter: Filter conditions

        Returns:
            Combined and ranked results
        """
        # Perform vector search
        vector_results = []
        if self.openai_client:
            query_embedding = self._generate_query_embedding(query)
            vector_raw = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=where_filter if where_filter else None
            )
            vector_results = self._convert_to_retrieval_results(vector_raw, method="vector")

        # Perform keyword search
        keyword_raw = self.collection.query(
            query_texts=[query],
            n_results=top_k,
            where=where_filter if where_filter else None
        )
        keyword_results = self._convert_to_retrieval_results(keyword_raw, method="keyword")

        # Combine and deduplicate results
        combined_results = self._combine_search_results(vector_results, keyword_results, query)

        # Return top_k results
        return combined_results[:top_k]

    def _combine_search_results(self, vector_results: List[RetrievalResult],
                               keyword_results: List[RetrievalResult],
                               query: str) -> List[RetrievalResult]:
        """Combine vector and keyword search results with hybrid scoring."""
        # Create a map to avoid duplicates
        result_map = {}

        # Add vector results with vector weight
        for result in vector_results:
            chunk_id = result.chunk.id
            if chunk_id not in result_map:
                result.retrieval_method = "vector"
                result_map[chunk_id] = result
            else:
                # Combine scores if duplicate
                existing = result_map[chunk_id]
                existing.relevance_score = max(existing.relevance_score, result.relevance_score)
                existing.retrieval_method = "hybrid"

        # Add keyword results with keyword weight
        for result in keyword_results:
            chunk_id = result.chunk.id
            if chunk_id not in result_map:
                result.retrieval_method = "keyword"
                # Calculate keyword match score
                result.keyword_match_score = self._calculate_keyword_match_score(query, result.chunk.content)
                result_map[chunk_id] = result
            else:
                # Enhance existing result with keyword score
                existing = result_map[chunk_id]
                keyword_score = self._calculate_keyword_match_score(query, result.chunk.content)
                existing.keyword_match_score = keyword_score
                # Combine scores using weighted average
                existing.relevance_score = (0.7 * existing.relevance_score + 0.3 * keyword_score)
                existing.retrieval_method = "hybrid"

        # Sort by combined relevance score
        combined_results = list(result_map.values())
        combined_results.sort(key=lambda x: x.relevance_score, reverse=True)

        return combined_results

    def _calculate_keyword_match_score(self, query: str, content: str) -> float:
        """Calculate keyword matching score between query and content."""
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())

        if not query_words:
            return 0.0

        # Exact matches
        exact_matches = len(query_words.intersection(content_words))
        exact_score = exact_matches / len(query_words)

        # Partial matches (substring matching)
        partial_matches = 0
        for query_word in query_words:
            if any(query_word in content_word for content_word in content_words):
                partial_matches += 1

        partial_score = partial_matches / len(query_words) * 0.5

        return min(exact_score + partial_score, 1.0)

    def _rerank_results(self, query: str, results: List[RetrievalResult],
                       analysis: QueryAnalysis) -> List[RetrievalResult]:
        """
        Rerank results based on query analysis and additional relevance factors.

        Args:
            query: Original query
            results: Initial retrieval results
            analysis: Query analysis

        Returns:
            Reranked results
        """
        if not results:
            return results

        # Calculate rerank scores for each result
        for result in results:
            rerank_score = self._calculate_rerank_score(query, result, analysis)
            result.rerank_score = rerank_score
            # Update overall relevance score
            result.relevance_score = (0.6 * result.relevance_score + 0.4 * rerank_score)

        # Sort by updated relevance score
        results.sort(key=lambda x: x.relevance_score, reverse=True)

        return results

    def _calculate_rerank_score(self, query: str, result: RetrievalResult,
                               analysis: QueryAnalysis) -> float:
        """Calculate reranking score based on multiple factors."""
        score = 0.0
        content = result.chunk.content.lower()

        # Intent matching bonus
        if analysis.intent == 'api_lookup' and 'bpy.' in content:
            score += 0.3
        elif analysis.intent == 'example_request' and 'example' in content:
            score += 0.3
        elif analysis.intent == 'best_practice' and any(term in content for term in ['best', 'practice', 'optimize']):
            score += 0.3

        # Keyword density bonus
        keyword_density = sum(1 for kw in analysis.keywords if kw.lower() in content) / max(len(analysis.keywords), 1)
        score += keyword_density * 0.2

        # Code requirement matching
        if analysis.requires_code:
            code_indicators = ['```', 'import', 'def ', 'class ', 'bpy.']
            if any(indicator in content for indicator in code_indicators):
                score += 0.2

        # Source type bonus based on query
        if result.chunk.source == KnowledgeSource.MCP_DOCS and any(term in query.lower() for term in ['molecular', 'atom', 'protein']):
            score += 0.2
        elif result.chunk.source == KnowledgeSource.BLENDER_API and 'bpy' in query.lower():
            score += 0.2

        # Content length penalty for overly long results
        content_length = len(result.chunk.content)
        if content_length > 2000:
            score -= 0.1

        return min(score, 1.0)

    def _convert_to_retrieval_results(self, raw_results: Dict, method: str = "unknown") -> List[RetrievalResult]:
        """Convert ChromaDB results to RetrievalResult objects."""
        retrieval_results = []

        if not raw_results or not raw_results.get('documents'):
            return retrieval_results

        documents = raw_results['documents'][0]
        metadatas = raw_results.get('metadatas', [None])[0] or []
        distances = raw_results.get('distances', [None])[0] or []
        ids = raw_results.get('ids', [None])[0] or []

        for i, doc in enumerate(documents):
            metadata = metadatas[i] if i < len(metadatas) else {}
            distance = distances[i] if i < len(distances) else 1.0
            doc_id = ids[i] if i < len(ids) else f"doc_{i}"

            # Create KnowledgeChunk
            chunk = KnowledgeChunk(
                id=doc_id,
                content=doc,
                source=KnowledgeSource(metadata.get('source', 'examples')),
                topic=metadata.get('topic', 'unknown'),
                subtopic=metadata.get('subtopic'),
                metadata=metadata
            )

            # Calculate relevance score (inverse of distance)
            relevance_score = max(0.0, 1.0 - distance)

            result = RetrievalResult(
                chunk=chunk,
                relevance_score=relevance_score,
                distance=distance,
                retrieval_method=method
            )

            retrieval_results.append(result)

        return retrieval_results

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for the knowledge agent."""
        return {
            'retrieval_stats': dict(self.retrieval_stats),
            'cache_size': len(self.query_cache),
            'cache_hit_rate': (self.retrieval_stats['cache_hits'] /
                              max(self.retrieval_stats['total_queries'], 1)) * 100,
            'query_success_rate': self.query_success_rate * 100,
            'tool_performance': {
                tool.name: {
                    'avg_reward': sum(rewards) / len(rewards) if rewards else 0.0,
                    'usage_count': len(rewards)
                }
                for tool, rewards in self.tool_performance_history.items()
            }
        }

    def clear_cache(self):
        """Clear the query cache."""
        with self.cache_lock:
            self.query_cache.clear()
            logger.info("Query cache cleared")

#!/usr/bin/env python3
"""
V2 Schema Demonstration Script

This script demonstrates the advanced features of the v2.0.0 specification generation,
including complex materials, modifiers, lighting, and MCP structures.
"""

import json
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from agents.spec_generation_agent import SpecGenerationAgent, SpecGenerationConfig
from agents.image_analysis_agent import (
    ImageAnalysisResult, DetectedShape, ShapeType, ColorInfo, BoundingBox, AnalysisGranularity
)
from models.specs.v2.models import ModelSpecificationV2
from models.specs.v2.migration import migrate_v1_to_v2, migrate_v2_to_v1


def create_sample_analysis_result(scenario: str) -> ImageAnalysisResult:
    """Create sample image analysis results for different scenarios."""
    
    if scenario == "advanced_scene":
        # Complex scene with multiple objects and materials
        shapes = [
            DetectedShape(
                shape_type=ShapeType.SPHERE,
                confidence=0.95,
                bounding_box=BoundingBox(x=50, y=50, width=80, height=80),
                color_info=ColorInfo(r=0.2, g=0.4, b=0.8, dominant_color_name="blue")
            ),
            DetectedShape(
                shape_type=ShapeType.CUBE,
                confidence=0.90,
                bounding_box=BoundingBox(x=200, y=100, width=60, height=60),
                color_info=ColorInfo(r=0.8, g=0.8, b=0.8, dominant_color_name="silver")
            ),
            DetectedShape(
                shape_type=ShapeType.TORUS,
                confidence=0.85,
                bounding_box=BoundingBox(x=350, y=150, width=100, height=100),
                color_info=ColorInfo(r=0.9, g=0.5, b=0.1, dominant_color_name="orange")
            )
        ]
        
        return ImageAnalysisResult(
            image_path="advanced_scene.png",
            detected_shapes=shapes,
            overall_confidence=0.90,
            analysis_granularity=AnalysisGranularity.DETAILED
        )
    
    elif scenario == "molecular":
        # Molecular structure scenario
        shapes = [
            DetectedShape(
                shape_type=ShapeType.SPHERE,
                confidence=0.95,
                bounding_box=BoundingBox(x=100, y=100, width=30, height=30),
                color_info=ColorInfo(r=0.3, g=0.3, b=0.3, dominant_color_name="black")
            ),
            DetectedShape(
                shape_type=ShapeType.SPHERE,
                confidence=0.90,
                bounding_box=BoundingBox(x=150, y=120, width=20, height=20),
                color_info=ColorInfo(r=1.0, g=1.0, b=1.0, dominant_color_name="white")
            )
        ]
        
        return ImageAnalysisResult(
            image_path="molecule.png",
            detected_shapes=shapes,
            overall_confidence=0.92,
            analysis_granularity=AnalysisGranularity.BASIC
        )
    
    else:  # simple_scene
        shapes = [
            DetectedShape(
                shape_type=ShapeType.CUBE,
                confidence=0.95,
                bounding_box=BoundingBox(x=100, y=100, width=50, height=50),
                color_info=ColorInfo(r=0.8, g=0.2, b=0.2, dominant_color_name="red")
            )
        ]
        
        return ImageAnalysisResult(
            image_path="simple_cube.png",
            detected_shapes=shapes,
            overall_confidence=0.95,
            analysis_granularity=AnalysisGranularity.BASIC
        )


def demo_v2_basic_features():
    """Demonstrate basic v2 features."""
    print("=== V2 Basic Features Demo ===")
    
    # Create v2 configuration
    config = SpecGenerationConfig(
        schema_version="v2.0.0",
        enable_complex_materials=True,
        enable_modifiers=True,
        enable_lighting=True
    )
    
    agent = SpecGenerationAgent(config=config)
    
    # Generate specification for advanced scene
    analysis_result = create_sample_analysis_result("advanced_scene")
    user_prefs = {
        "complexity_level": "advanced",
        "units": "meters"
    }
    
    result = agent.generate_specification(analysis_result, user_prefs)
    
    print(f"✓ Generated v2 specification with {len(result.specification['objects'])} objects")
    print(f"✓ Validation passed: {result.validation_passed}")
    print(f"✓ Schema version: {result.specification['schema_version']}")
    print(f"✓ Complexity level: {result.specification['model_info']['complexity_level']}")
    print(f"✓ Model type: {result.specification['model_info']['model_type']}")
    
    # Check for v2 features
    if "lighting" in result.specification:
        print(f"✓ Lighting system with {len(result.specification['lighting']['lights'])} lights")
    
    if "groups" in result.specification:
        print(f"✓ Object groups: {len(result.specification['groups'])} groups")
    
    # Check for advanced materials and modifiers
    advanced_materials = 0
    total_modifiers = 0
    
    for obj in result.specification['objects']:
        if obj.get('material', {}).get('type') in ['pbr', 'glass', 'emission']:
            advanced_materials += 1
        if 'modifiers' in obj:
            total_modifiers += len(obj['modifiers'])
    
    print(f"✓ Advanced materials: {advanced_materials}")
    print(f"✓ Total modifiers: {total_modifiers}")
    
    # Validate with Pydantic
    try:
        spec_model = ModelSpecificationV2(**result.specification)
        print("✓ Pydantic validation passed")
    except Exception as e:
        print(f"✗ Pydantic validation failed: {e}")
    
    return result.specification


def demo_mcp_structures():
    """Demonstrate MCP structures generation."""
    print("\n=== MCP Structures Demo ===")
    
    config = SpecGenerationConfig(
        schema_version="v2.0.0",
        enable_mcp_structures=True
    )
    
    agent = SpecGenerationAgent(config=config)
    
    # Generate molecular specification
    analysis_result = create_sample_analysis_result("molecular")
    user_prefs = {
        "model_type": "molecular",
        "complexity_level": "intermediate"
    }
    
    result = agent.generate_specification(analysis_result, user_prefs)
    
    print(f"✓ Generated molecular specification")
    print(f"✓ Model type: {result.specification['model_info']['model_type']}")
    
    if "mcp_structures" in result.specification:
        mcp_count = len(result.specification['mcp_structures'])
        print(f"✓ MCP structures: {mcp_count}")
        
        for mcp in result.specification['mcp_structures']:
            print(f"  - {mcp['name']} ({mcp['structure_type']})")
            if mcp['structure_type'] == 'molecular' and 'molecular' in mcp:
                mol_data = mcp['molecular']
                print(f"    Atoms: {len(mol_data['atoms'])}, Bonds: {len(mol_data['bonds'])}")
    
    return result.specification


def demo_schema_migration():
    """Demonstrate schema migration between v1 and v2."""
    print("\n=== Schema Migration Demo ===")
    
    # Create a v1 specification
    v1_spec = {
        "schema_version": "v1.0.0",
        "model_info": {
            "name": "Migration Test Model",
            "description": "A test model for migration",
            "created_at": "2025-01-19T10:00:00Z",
            "tags": ["test", "migration"]
        },
        "scene_settings": {
            "units": "meters"
        },
        "objects": [
            {
                "id": "cube_1",
                "name": "Test Cube",
                "geometry": {
                    "type": "cube",
                    "size": 2.0
                },
                "material": {
                    "type": "basic",
                    "name": "Red Material",
                    "color": {"r": 0.8, "g": 0.2, "b": 0.2, "a": 1.0}
                },
                "visible": True
            }
        ]
    }
    
    print("✓ Created v1 specification")
    
    # Migrate to v2
    v2_spec = migrate_v1_to_v2(v1_spec)
    print("✓ Migrated v1 to v2")
    print(f"  - Schema version: {v2_spec['schema_version']}")
    print(f"  - Added complexity_level: {v2_spec['model_info']['complexity_level']}")
    print(f"  - Added model_type: {v2_spec['model_info']['model_type']}")
    
    # Validate v2 specification
    try:
        ModelSpecificationV2(**v2_spec)
        print("✓ V2 specification validation passed")
    except Exception as e:
        print(f"✗ V2 specification validation failed: {e}")
    
    # Migrate back to v1
    v1_migrated = migrate_v2_to_v1(v2_spec)
    print("✓ Migrated v2 back to v1")
    print(f"  - Schema version: {v1_migrated['schema_version']}")
    
    # Check data preservation
    original_name = v1_spec['model_info']['name']
    migrated_name = v1_migrated['model_info']['name']
    
    if original_name == migrated_name:
        print("✓ Model name preserved during round-trip migration")
    else:
        print(f"✗ Model name changed: {original_name} -> {migrated_name}")
    
    return v1_spec, v2_spec, v1_migrated


def save_example_files(specs: dict):
    """Save example specifications to files."""
    print("\n=== Saving Example Files ===")
    
    output_dir = Path("examples/generated_v2_specs")
    output_dir.mkdir(exist_ok=True)
    
    for name, spec in specs.items():
        filename = output_dir / f"{name}.json"
        with open(filename, 'w') as f:
            json.dump(spec, f, indent=2)
        print(f"✓ Saved {filename}")


def main():
    """Main demonstration function."""
    print("V2 Schema Advanced Features Demonstration")
    print("=" * 50)
    
    specs = {}
    
    # Demo basic v2 features
    specs['advanced_scene'] = demo_v2_basic_features()
    
    # Demo MCP structures
    specs['molecular_structure'] = demo_mcp_structures()
    
    # Demo schema migration
    v1_spec, v2_spec, v1_migrated = demo_schema_migration()
    specs['v1_original'] = v1_spec
    specs['v2_migrated'] = v2_spec
    specs['v1_round_trip'] = v1_migrated
    
    # Save example files
    save_example_files(specs)
    
    print("\n=== Summary ===")
    print("✓ V2 schema supports complex materials (PBR, Glass, Emission)")
    print("✓ V2 schema supports modifiers (Array, Mirror, Bevel, etc.)")
    print("✓ V2 schema supports advanced lighting systems")
    print("✓ V2 schema supports MCP molecular structures")
    print("✓ V2 schema supports object groups and hierarchies")
    print("✓ Schema migration works bidirectionally")
    print("✓ All features validated with Pydantic models")
    
    print(f"\n✓ Generated {len(specs)} example specifications")
    print("✓ Task 4.2 completed successfully!")


if __name__ == "__main__":
    main()

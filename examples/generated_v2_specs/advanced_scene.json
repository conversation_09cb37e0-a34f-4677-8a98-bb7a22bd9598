{"schema_version": "v2.0.0", "model_info": {"name": "Generated_Model_d75505aa", "description": "3D model containing a sphere, a cube, and a torus", "created_at": "2025-07-19T13:10:37.530520", "tags": ["generated", "3d-model", "sphere", "cube", "torus", "blue", "silver", "orange"], "complexity_level": "advanced", "model_type": "standard"}, "scene_settings": {"units": "meters"}, "objects": [{"id": "object_1", "name": "Sphere_1", "geometry": {"type": "sphere", "radius": 800.0, "subdivisions": 4}, "visible": true, "transform": {"position": {"x": 895.0, "y": 0.0, "z": -895.0}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}, "scale": {"x": 1.0, "y": 1.0, "z": 1.0}}, "material": {"name": "blue_material", "color": {"r": 0.2, "g": 0.4, "b": 0.8, "a": 1.0}, "type": "glass", "transmission": 0.9, "ior": 1.45, "roughness": 0.0}, "modifiers": [{"type": "subdivision_surface", "name": "Smooth Surface", "levels": 2, "render_levels": 3, "enabled": true}, {"type": "mirror", "name": "Mirror X", "axis": ["x"], "merge": true, "enabled": true}]}, {"id": "object_2", "name": "Cube_2", "geometry": {"type": "cube", "size": 600.0}, "visible": true, "transform": {"position": {"x": 2295.0, "y": 0.0, "z": -1295.0}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}, "scale": {"x": 1.0, "y": 1.0, "z": 1.0}}, "material": {"name": "silver_material", "color": {"r": 0.8, "g": 0.8, "b": 0.8, "a": 1.0}, "type": "pbr", "metallic": 0.8, "roughness": 0.2}, "modifiers": [{"type": "mirror", "name": "Mirror X", "axis": ["x"], "merge": true, "enabled": true}, {"type": "array", "name": "<PERSON><PERSON><PERSON>", "count": 3, "offset": {"x": 2.5, "y": 0.0, "z": 0.0}, "enabled": true}, {"type": "bevel", "name": "<PERSON>", "width": 0.1, "segments": 2, "enabled": true}]}, {"id": "object_3", "name": "Torus_3", "geometry": {"type": "cube", "size": 2.0}, "visible": true, "transform": {"position": {"x": 3995.0, "y": 0.0, "z": -1995.0}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}, "scale": {"x": 1.0, "y": 1.0, "z": 1.0}}, "material": {"name": "orange_material", "color": {"r": 0.9, "g": 0.5, "b": 0.1, "a": 1.0}, "type": "emission", "emission": {"r": 0.9, "g": 0.5, "b": 0.1, "a": 1.0}, "emission_strength": 2.0}, "modifiers": [{"type": "subdivision_surface", "name": "Smooth Surface", "levels": 2, "render_levels": 3, "enabled": true}]}], "lighting": {"lights": [{"id": "main_sun", "type": "sun", "name": "Main Sun Light", "energy": 5.0, "color": {"r": 1.0, "g": 0.95, "b": 0.8, "a": 1.0}, "transform": {"rotation": {"x": 0.785398, "y": 0.0, "z": 0.785398}}}, {"id": "fill_light", "type": "area", "name": "Fill Light", "energy": 2.0, "size": 2.0, "color": {"r": 0.8, "g": 0.9, "b": 1.0, "a": 1.0}, "transform": {"position": {"x": -3.0, "y": 3.0, "z": 2.0}, "rotation": {"x": 0.523599, "y": -0.785398, "z": 0.0}}}], "global_illumination": {"enabled": true, "strength": 1.0}}}
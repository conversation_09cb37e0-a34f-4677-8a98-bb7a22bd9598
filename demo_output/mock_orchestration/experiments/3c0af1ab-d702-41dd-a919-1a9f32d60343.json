{"experiment_id": "3c0af1ab-d702-41dd-a919-1a9f32d60343", "name": "orchestration_dbc01c05", "description": "End-to-end model generation from demo_output/test_image.png", "created_at": "2025-07-19T16:07:17.995419", "updated_at": "2025-07-19T16:07:17.996075", "status": "completed", "parameters": {}, "tags": ["orchestration", "end-to-end"], "metrics_history": [{"step": 0, "timestamp": "2025-07-19T16:07:17.995891", "metrics": {"success": true, "total_time": 0.00022029876708984375, "inner_loop_iterations": 1, "outer_loop_iterations": 1, "quality_score": 0.0}}], "final_metrics": {"success": true, "total_time": 0.00022029876708984375, "inner_loop_iterations": 1, "outer_loop_iterations": 1, "quality_score": 0.0}, "artifacts": {}, "model_type": null, "framework": null, "dataset": null, "environment": {}, "git_commit": null}
{"trajectory_id": "175c12ff-c64c-41c6-91fb-6905124bd0ab", "agent_type": "code_generation_agent", "start_time": "2025-07-19T15:28:57.367307", "end_time": "2025-07-19T15:28:57.567670", "states": [{"state_id": "00c0c2e7-8cf5-45b8-977b-059cf000c510", "observation": [2.0, 0.3, 0.58, 1.5, 0.0, 4.0], "task_type": "code_generation", "task_progress": 0.0, "context_complexity": 0.58, "timestamp": 1752910137.367372, "metadata": {}}], "actions": [{"action_id": "703347eb-4039-41ab-a698-e68de8c07c39", "action_type": "strategy_selection", "action_value": 1, "timestamp": 1752910137.3673952, "context": {"spec_complexity": 0.58}, "metadata": {}}], "rewards": [{"reward_value": 0.9, "reward_components": {"code_quality": 0.7, "generation_time": 0.2}, "success": true, "timestamp": 1752910137.5676284, "metadata": {}}, {"reward_value": 1.0, "reward_components": {"task_completion": 1.0}, "success": true, "timestamp": 1752910137.5676587, "metadata": {"result": "Generated code for spec with complexity 0.58"}}], "total_reward": 1.9, "success": true, "episode_length": 1, "task_completed": true, "initial_context": {"spec_id": 4, "spec_type": "plane"}, "final_context": {"result": "Generated code for spec with complexity 0.58"}, "error_messages": []}
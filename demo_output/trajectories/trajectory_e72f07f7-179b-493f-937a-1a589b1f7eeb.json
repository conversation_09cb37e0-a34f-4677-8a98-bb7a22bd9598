{"trajectory_id": "e72f07f7-179b-493f-937a-1a589b1f7eeb", "agent_type": "code_generation_agent", "start_time": "2025-07-19T15:28:56.760462", "end_time": "2025-07-19T15:28:56.960810", "states": [{"state_id": "ab244600-25dd-4f8b-9727-041b39e3d998", "observation": [2.0, 0.3, 0.52, 1.5, 0.0, 4.0], "task_type": "code_generation", "task_progress": 0.0, "context_complexity": 0.52, "timestamp": 1752910136.7605407, "metadata": {}}], "actions": [{"action_id": "19c5e433-75d2-490e-b285-7e64732c0b31", "action_type": "strategy_selection", "action_value": 1, "timestamp": 1752910136.7605703, "context": {"spec_complexity": 0.52}, "metadata": {}}], "rewards": [{"reward_value": 0.9, "reward_components": {"code_quality": 0.7, "generation_time": 0.2}, "success": true, "timestamp": 1752910136.960737, "metadata": {}}, {"reward_value": 1.0, "reward_components": {"task_completion": 1.0}, "success": true, "timestamp": 1752910136.960789, "metadata": {"result": "Generated code for spec with complexity 0.52"}}], "total_reward": 1.9, "success": true, "episode_length": 1, "task_completed": true, "initial_context": {"spec_id": 1, "spec_type": "sphere"}, "final_context": {"result": "Generated code for spec with complexity 0.52"}, "error_messages": []}
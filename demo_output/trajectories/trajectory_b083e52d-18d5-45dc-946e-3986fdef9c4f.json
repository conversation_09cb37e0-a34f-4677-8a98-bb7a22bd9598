{"trajectory_id": "b083e52d-18d5-45dc-946e-3986fdef9c4f", "agent_type": "code_generation_agent", "start_time": "2025-07-19T15:28:56.558377", "end_time": "2025-07-19T15:28:56.758789", "states": [{"state_id": "f4483fe5-de29-4a82-9985-214ee68c045e", "observation": [2.0, 0.3, 0.39, 1.5, 0.0, 4.0], "task_type": "code_generation", "task_progress": 0.0, "context_complexity": 0.39, "timestamp": 1752910136.558475, "metadata": {}}], "actions": [{"action_id": "610d4e14-d806-4528-87a7-d0557800e040", "action_type": "strategy_selection", "action_value": 1, "timestamp": 1752910136.558509, "context": {"spec_complexity": 0.39}, "metadata": {}}], "rewards": [{"reward_value": 0.9, "reward_components": {"code_quality": 0.7, "generation_time": 0.2}, "success": true, "timestamp": 1752910136.7587304, "metadata": {}}, {"reward_value": 1.0, "reward_components": {"task_completion": 1.0}, "success": true, "timestamp": 1752910136.758771, "metadata": {"result": "Generated code for spec with complexity 0.39"}}], "total_reward": 1.9, "success": true, "episode_length": 1, "task_completed": true, "initial_context": {"spec_id": 0, "spec_type": "cube"}, "final_context": {"result": "Generated code for spec with complexity 0.39"}, "error_messages": []}
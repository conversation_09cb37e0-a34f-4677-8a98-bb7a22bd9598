{"trajectory_id": "464dfc99-6bcd-4675-80da-8e2c574409e1", "agent_type": "code_generation_agent", "start_time": "2025-07-19T15:28:57.165426", "end_time": "2025-07-19T15:28:57.365741", "states": [{"state_id": "63f035ce-94db-486c-bdb1-71c333dac68b", "observation": [2.0, 0.3, 0.5, 1.5, 0.0, 4.0], "task_type": "code_generation", "task_progress": 0.0, "context_complexity": 0.5, "timestamp": 1752910137.1655092, "metadata": {}}], "actions": [{"action_id": "78e04f1b-1692-4b19-a2b2-bb0812657354", "action_type": "strategy_selection", "action_value": 1, "timestamp": 1752910137.1655462, "context": {"spec_complexity": 0.5}, "metadata": {}}], "rewards": [{"reward_value": 0.9, "reward_components": {"code_quality": 0.7, "generation_time": 0.2}, "success": true, "timestamp": 1752910137.365686, "metadata": {}}, {"reward_value": 1.0, "reward_components": {"task_completion": 1.0}, "success": true, "timestamp": 1752910137.3657267, "metadata": {"result": "Generated code for spec with complexity 0.50"}}], "total_reward": 1.9, "success": true, "episode_length": 1, "task_completed": true, "initial_context": {"spec_id": 3, "spec_type": "torus"}, "final_context": {"result": "Generated code for spec with complexity 0.50"}, "error_messages": []}
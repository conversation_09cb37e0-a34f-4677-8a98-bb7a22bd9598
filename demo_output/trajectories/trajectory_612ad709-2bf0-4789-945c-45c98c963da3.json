{"trajectory_id": "612ad709-2bf0-4789-945c-45c98c963da3", "agent_type": "code_generation_agent", "start_time": "2025-07-19T15:28:56.962856", "end_time": "2025-07-19T15:28:57.163343", "states": [{"state_id": "87335671-c25e-45e0-99b1-ad0a6e6ec1f2", "observation": [2.0, 0.3, 0.59, 1.5, 0.0, 4.0], "task_type": "code_generation", "task_progress": 0.0, "context_complexity": 0.59, "timestamp": 1752910136.9629579, "metadata": {}}], "actions": [{"action_id": "9faf56dd-59b7-439d-9bd9-360e514d395d", "action_type": "strategy_selection", "action_value": 1, "timestamp": 1752910136.9629915, "context": {"spec_complexity": 0.59}, "metadata": {}}], "rewards": [{"reward_value": 0.9, "reward_components": {"code_quality": 0.7, "generation_time": 0.2}, "success": true, "timestamp": 1752910137.1632512, "metadata": {}}, {"reward_value": 1.0, "reward_components": {"task_completion": 1.0}, "success": true, "timestamp": 1752910137.1633067, "metadata": {"result": "Generated code for spec with complexity 0.59"}}], "total_reward": 1.9, "success": true, "episode_length": 1, "task_completed": true, "initial_context": {"spec_id": 2, "spec_type": "cylinder"}, "final_context": {"result": "Generated code for spec with complexity 0.59"}, "error_messages": []}
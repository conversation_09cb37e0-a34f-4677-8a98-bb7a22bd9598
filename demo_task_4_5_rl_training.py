"""
Demo script for Task 4.5: <PERSON> 策略训练与持续优化

This script demonstrates the RL training pipeline including trajectory collection,
policy optimization, and continuous learning capabilities.
"""

import os
import sys
import json
import time
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(__file__))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from rl_training.rl_trainer import RLTrainer
from rl_training.training_config import (
    TrainingConfig, TrainingMode, OptimizationAlgorithm,
    HyperparameterConfig, EvaluationConfig
)
from rl_training.policy_optimizer import OptimizationConfig
from rl_training.trajectory_collector import TrajectoryCollector
from rl_training.enhanced_agent_base import EnhancedAgentBase

# Mock enhanced agents for demonstration
class MockKnowledgeAgent(EnhancedAgentBase):
    """Mock knowledge agent for demonstration."""
    
    def __init__(self, trajectory_collector=None):
        super().__init__(
            agent_type="knowledge_agent",
            trajectory_collector=trajectory_collector,
            enable_trajectory_collection=True
        )
    
    def execute_task(self, query: str):
        """Mock knowledge query execution."""
        # Simulate state
        self.record_state(
            observation=[1.0, 0.5, 0.8, 2.0, 0.0, 3.5],
            task_type="knowledge_query",
            task_progress=0.0,
            context_complexity=2.5
        )
        
        # Simulate tool selection action
        self.record_action(
            action_type="tool_selection",
            action_value=2,  # Vector search
            context={"query": query, "query_length": len(query)}
        )
        
        # Simulate processing
        time.sleep(0.1)
        
        # Simulate reward based on query complexity
        success = len(query) > 10  # Simple success criteria
        reward = 0.8 if success else -0.2
        
        self.record_reward(
            reward_value=reward,
            reward_components={"relevance": 0.6, "efficiency": 0.2},
            success=success
        )
        
        return f"Knowledge result for: {query}"


class MockCodeGenerationAgent(EnhancedAgentBase):
    """Mock code generation agent for demonstration."""
    
    def __init__(self, trajectory_collector=None):
        super().__init__(
            agent_type="code_generation_agent", 
            trajectory_collector=trajectory_collector,
            enable_trajectory_collection=True
        )
    
    def execute_task(self, spec: dict):
        """Mock code generation execution."""
        # Simulate state
        complexity = len(str(spec)) / 100.0
        self.record_state(
            observation=[2.0, 0.3, complexity, 1.5, 0.0, 4.0],
            task_type="code_generation",
            task_progress=0.0,
            context_complexity=complexity
        )
        
        # Simulate strategy selection
        self.record_action(
            action_type="strategy_selection",
            action_value=1,  # LLM-assisted
            context={"spec_complexity": complexity}
        )
        
        # Simulate processing
        time.sleep(0.2)
        
        # Simulate reward based on spec complexity
        success = complexity < 2.0  # Simpler specs are more likely to succeed
        reward = 0.9 if success else -0.1
        
        self.record_reward(
            reward_value=reward,
            reward_components={"code_quality": 0.7, "generation_time": 0.2},
            success=success
        )
        
        return f"Generated code for spec with complexity {complexity:.2f}"


def demo_trajectory_collection():
    """Demonstrate trajectory data collection."""
    print("\n" + "="*60)
    print("🔄 Demo 1: Trajectory Data Collection")
    print("="*60)
    
    # Initialize trajectory collector
    collector = TrajectoryCollector(
        storage_path="demo_output/trajectories",
        max_trajectories=1000,
        auto_save=True
    )
    
    # Create mock agents
    knowledge_agent = MockKnowledgeAgent(collector)
    code_agent = MockCodeGenerationAgent(collector)
    
    # Collect some trajectories
    queries = [
        "How to create a cube in Blender?",
        "Blender Python API for materials",
        "Create sphere with PBR material",
        "Animation keyframes in Blender",
        "Modifier array in Python"
    ]
    
    specs = [
        {"type": "cube", "position": [0, 0, 0]},
        {"type": "sphere", "material": "pbr", "radius": 2.0},
        {"type": "cylinder", "height": 4.0, "modifiers": ["array"]},
        {"type": "torus", "animation": {"rotation": True}},
        {"type": "plane", "material": "glass", "scale": [2, 2, 1]}
    ]
    
    print(f"📊 Collecting trajectories from {len(queries)} knowledge queries and {len(specs)} code generation tasks...")
    
    # Knowledge agent trajectories
    for i, query in enumerate(queries):
        try:
            result = knowledge_agent.execute_task_with_tracking(
                query, 
                {"query_id": i, "query_type": "blender_api"}
            )
            print(f"✅ Knowledge query {i+1}: {result[:50]}...")
        except Exception as e:
            print(f"❌ Knowledge query {i+1} failed: {e}")
    
    # Code generation agent trajectories
    for i, spec in enumerate(specs):
        try:
            result = code_agent.execute_task_with_tracking(
                spec,
                {"spec_id": i, "spec_type": spec.get("type", "unknown")}
            )
            print(f"✅ Code generation {i+1}: {result[:50]}...")
        except Exception as e:
            print(f"❌ Code generation {i+1} failed: {e}")
    
    # Show statistics
    stats = collector.get_statistics()
    print(f"\n📈 Collection Statistics:")
    print(f"   Total trajectories: {stats['total_trajectories']}")
    print(f"   Success rate: {stats['success_rate']:.2%}")
    print(f"   Average reward: {stats['avg_reward']:.3f}")
    
    for agent_type, agent_stats in stats['agent_types'].items():
        print(f"   {agent_type}: {agent_stats['count']} trajectories, "
              f"{agent_stats['success_rate']:.2%} success rate")
    
    return collector


def demo_policy_optimization(collector):
    """Demonstrate policy optimization."""
    print("\n" + "="*60)
    print("🧠 Demo 2: Policy Optimization")
    print("="*60)
    
    # Configure training
    training_config = TrainingConfig(
        experiment_name="demo_policy_optimization",
        training_mode=TrainingMode.OFFLINE,
        algorithm=OptimizationAlgorithm.PPO,
        max_training_iterations=50,  # Reduced for demo
        min_trajectories_required=5,
        hyperparameters=HyperparameterConfig(
            learning_rate=0.001,
            train_batch_size=200,
            num_sgd_iter=3
        )
    )
    
    optimization_config = OptimizationConfig(
        max_iterations=50,
        eval_frequency=10,
        checkpoint_frequency=20,
        early_stopping=True,
        patience=15
    )
    
    # Initialize trainer
    trainer = RLTrainer(
        config=training_config,
        workspace_dir="demo_output/rl_training"
    )
    
    # Set trajectory collector
    trainer.trajectory_collector = collector
    
    print(f"🎯 Training configuration:")
    print(f"   Algorithm: {training_config.algorithm.value}")
    print(f"   Max iterations: {optimization_config.max_iterations}")
    print(f"   Learning rate: {training_config.hyperparameters.learning_rate}")
    
    try:
        # Train knowledge agent policy
        print(f"\n🔧 Training knowledge agent policy...")
        knowledge_result = trainer.train_agent_policy(
            agent_type="knowledge_agent",
            optimization_config=optimization_config
        )
        
        print(f"✅ Knowledge agent training completed:")
        print(f"   Best reward: {knowledge_result['optimization_result']['best_reward']:.4f}")
        print(f"   Success rate: {knowledge_result['optimization_result']['success_rate']:.3f}")
        print(f"   Training time: {knowledge_result['optimization_result']['training_time']:.1f}s")
        
        # Train code generation agent policy
        print(f"\n🔧 Training code generation agent policy...")
        code_result = trainer.train_agent_policy(
            agent_type="code_generation_agent",
            optimization_config=optimization_config
        )
        
        print(f"✅ Code generation agent training completed:")
        print(f"   Best reward: {code_result['optimization_result']['best_reward']:.4f}")
        print(f"   Success rate: {code_result['optimization_result']['success_rate']:.3f}")
        print(f"   Training time: {code_result['optimization_result']['training_time']:.1f}s")
        
        return trainer, [knowledge_result, code_result]
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        print("   This is expected in demo mode due to limited trajectory data")
        return trainer, []


def demo_model_evaluation(trainer):
    """Demonstrate model evaluation."""
    print("\n" + "="*60)
    print("📊 Demo 3: Model Evaluation")
    print("="*60)
    
    try:
        # Evaluate trained policies
        evaluation_results = trainer.evaluate_policies(
            agent_types=["knowledge_agent", "code_generation_agent"],
            num_episodes=20  # Reduced for demo
        )
        
        print(f"🎯 Evaluation Results:")
        for agent_type, results in evaluation_results.items():
            if "error" in results:
                print(f"   {agent_type}: {results['error']}")
            else:
                print(f"   {agent_type}:")
                print(f"     Average reward: {results['avg_reward']:.3f}")
                print(f"     Success rate: {results['success_rate']:.3f}")
                print(f"     Average episode length: {results['avg_episode_length']:.1f}")
        
        return evaluation_results
        
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        return {}


def demo_mlops_integration(trainer):
    """Demonstrate MLOps integration."""
    print("\n" + "="*60)
    print("🔄 Demo 4: MLOps Integration")
    print("="*60)
    
    # Get training statistics
    stats = trainer.get_training_statistics()
    
    print(f"📈 Training Statistics:")
    print(f"   Trajectory statistics: {stats['trajectory_statistics']['total_trajectories']} trajectories")
    print(f"   Training history: {stats['training_history']} training sessions")
    print(f"   Optimization history: {stats['optimization_history']} optimizations")
    
    # Export training data
    export_path = "demo_output/training_data_export.json"
    exported_file = trainer.export_training_data(export_path)
    print(f"📁 Exported training data to: {exported_file}")
    
    # Show model registry if available
    if trainer.model_registry:
        models = trainer.model_registry.list_models()
        print(f"🏷️  Registered models: {len(models)}")
        for model in models[:3]:  # Show first 3 models
            print(f"   {model.name} v{model.version} ({model.status.value})")
    
    return stats


def demo_continuous_optimization_setup():
    """Demonstrate continuous optimization setup."""
    print("\n" + "="*60)
    print("🔄 Demo 5: Continuous Optimization Setup")
    print("="*60)
    
    # Configure for continuous optimization
    continuous_config = TrainingConfig(
        experiment_name="continuous_optimization",
        training_mode=TrainingMode.ONLINE,
        max_training_iterations=100,
        min_trajectories_required=10,
        track_experiments=True,
        register_models=True,
        auto_deploy=False  # Keep false for demo
    )
    
    print(f"⚙️  Continuous optimization configuration:")
    print(f"   Training mode: {continuous_config.training_mode.value}")
    print(f"   Auto-deploy: {continuous_config.auto_deploy}")
    print(f"   Experiment tracking: {continuous_config.track_experiments}")
    print(f"   Model registration: {continuous_config.register_models}")
    
    print(f"\n🔄 Continuous optimization would:")
    print(f"   • Monitor for new trajectory data every hour")
    print(f"   • Retrain policies when sufficient new data is available")
    print(f"   • Automatically register improved models")
    print(f"   • Update deployed models based on performance thresholds")
    
    print(f"\n💡 To start continuous optimization:")
    print(f"   trainer = RLTrainer(continuous_config)")
    print(f"   trainer.continuous_optimization(check_interval=3600)")


def main():
    """Run all RL training demos."""
    print("🚀 Task 4.5: Ray RLlib 策略训练与持续优化 - Demo")
    print("=" * 80)
    
    try:
        # Demo 1: Trajectory Collection
        collector = demo_trajectory_collection()
        
        # Demo 2: Policy Optimization
        trainer, training_results = demo_policy_optimization(collector)
        
        # Demo 3: Model Evaluation
        evaluation_results = demo_model_evaluation(trainer)
        
        # Demo 4: MLOps Integration
        mlops_stats = demo_mlops_integration(trainer)
        
        # Demo 5: Continuous Optimization Setup
        demo_continuous_optimization_setup()
        
        print("\n" + "="*80)
        print("✅ All RL training demos completed successfully!")
        print("\nKey achievements:")
        print("- ✅ Trajectory data collection from agent interactions")
        print("- ✅ Policy optimization using collected trajectories")
        print("- ✅ Model evaluation and performance metrics")
        print("- ✅ MLOps integration with model registry and experiment tracking")
        print("- ✅ Continuous optimization pipeline setup")
        print("\n📊 Demo Summary:")
        print(f"   Trajectories collected: {collector.get_statistics()['total_trajectories']}")
        print(f"   Training sessions: {len(training_results)}")
        print(f"   Models evaluated: {len(evaluation_results)}")
        print(f"   Export files created: demo_output/")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

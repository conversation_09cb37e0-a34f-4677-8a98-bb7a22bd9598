"""
Main RL Trainer for Strategy Training and Continuous Optimization

This module provides the main training interface for RL strategy optimization,
integrating trajectory collection, policy optimization, and MLOps workflows.
"""

import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import uuid

# MLOps imports
from mlops.model_registry import ModelRegistry, ModelType, ModelStatus
from mlops.experiment_tracker import ExperimentTracker
from mlops.version_manager import ModelVersionManager

from .training_config import TrainingConfig, TrainingMode
from .trajectory_collector import TrajectoryCollector
from .policy_optimizer import PolicyOptimizer, OptimizationConfig

logger = logging.getLogger(__name__)


class RLTrainer:
    """Main RL trainer for strategy training and continuous optimization."""
    
    def __init__(self, 
                 config: TrainingConfig,
                 workspace_dir: str = "rl_training_workspace"):
        """Initialize RL trainer."""
        self.config = config
        self.workspace_dir = Path(workspace_dir)
        self.workspace_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.trajectory_collector = TrajectoryCollector(
            storage_path=str(self.workspace_dir / "trajectories"),
            max_trajectories=10000,
            auto_save=True
        )
        
        self.policy_optimizer = PolicyOptimizer(
            config=config,
            trajectory_collector=self.trajectory_collector,
            output_dir=str(self.workspace_dir / "optimization_output")
        )
        
        # MLOps integration
        if config.track_experiments:
            self.experiment_tracker = ExperimentTracker(
                storage_path=str(self.workspace_dir / "experiments")
            )
        else:
            self.experiment_tracker = None
        
        if config.register_models:
            self.model_registry = ModelRegistry(
                registry_path=self.workspace_dir / "model_registry.json"
            )
        else:
            self.model_registry = None
        
        self.version_manager = ModelVersionManager(
            models_dir=self.workspace_dir / "versions"
        )
        
        # Training state
        self.current_experiment_id = None
        self.training_history: List[Dict[str, Any]] = []
        
        logger.info(f"RLTrainer initialized with mode: {config.training_mode.value}")
    
    def start_experiment(self, 
                        experiment_name: str,
                        description: str = "",
                        tags: List[str] = None) -> str:
        """Start a new training experiment."""
        if self.experiment_tracker:
            self.current_experiment_id = self.experiment_tracker.start_experiment(
                name=experiment_name,
                description=description,
                tags=tags or [],
                config=self.config.__dict__
            )
            logger.info(f"Started experiment: {experiment_name} ({self.current_experiment_id})")
        else:
            self.current_experiment_id = str(uuid.uuid4())
            logger.info(f"Started experiment: {experiment_name} (tracking disabled)")
        
        return self.current_experiment_id
    
    def train_agent_policy(self, 
                          agent_type: str,
                          optimization_config: OptimizationConfig = None) -> Dict[str, Any]:
        """Train policy for specific agent type."""
        if not self.current_experiment_id:
            self.start_experiment(f"train_{agent_type}_{int(time.time())}")
        
        logger.info(f"Training policy for {agent_type}")
        
        # Check trajectory availability
        trajectories = self.trajectory_collector.get_trajectories(agent_type=agent_type)
        if len(trajectories) < self.config.min_trajectories_required:
            raise ValueError(f"Insufficient trajectories for {agent_type}: {len(trajectories)} < {self.config.min_trajectories_required}")
        
        # Log training start
        if self.experiment_tracker:
            self.experiment_tracker.log_metrics(
                self.current_experiment_id,
                {
                    f"{agent_type}_training_start": time.time(),
                    f"{agent_type}_available_trajectories": len(trajectories)
                }
            )
        
        # Optimize policy
        optimization_result = self.policy_optimizer.optimize_policy(
            agent_type=agent_type,
            optimization_config=optimization_config
        )
        
        # Register model if enabled
        model_id = None
        if self.model_registry:
            model_id = self._register_trained_model(agent_type, optimization_result)
        
        # Log training completion
        if self.experiment_tracker:
            self.experiment_tracker.log_metrics(
                self.current_experiment_id,
                {
                    f"{agent_type}_training_end": time.time(),
                    f"{agent_type}_final_reward": optimization_result.final_reward,
                    f"{agent_type}_best_reward": optimization_result.best_reward,
                    f"{agent_type}_success_rate": optimization_result.success_rate,
                    f"{agent_type}_training_time": optimization_result.training_time
                }
            )
        
        # Create training summary
        training_summary = {
            "agent_type": agent_type,
            "experiment_id": self.current_experiment_id,
            "optimization_result": optimization_result.to_dict(),
            "model_id": model_id,
            "timestamp": datetime.now().isoformat()
        }
        
        self.training_history.append(training_summary)
        
        logger.info(f"Completed training for {agent_type}: reward={optimization_result.best_reward:.4f}")
        
        return training_summary
    
    def train_all_agents(self, 
                        agent_types: List[str] = None,
                        optimization_config: OptimizationConfig = None) -> Dict[str, Any]:
        """Train policies for all specified agent types."""
        if agent_types is None:
            agent_types = ["knowledge_agent", "code_generation_agent"]
        
        experiment_name = f"train_all_agents_{int(time.time())}"
        self.start_experiment(experiment_name, "Training all agent policies")
        
        results = {}
        overall_start_time = time.time()
        
        for agent_type in agent_types:
            try:
                result = self.train_agent_policy(agent_type, optimization_config)
                results[agent_type] = result
                logger.info(f"Successfully trained {agent_type}")
            except Exception as e:
                logger.error(f"Failed to train {agent_type}: {e}")
                results[agent_type] = {"error": str(e)}
        
        overall_training_time = time.time() - overall_start_time
        
        # Log overall results
        if self.experiment_tracker:
            self.experiment_tracker.log_metrics(
                self.current_experiment_id,
                {
                    "overall_training_time": overall_training_time,
                    "agents_trained": len([r for r in results.values() if "error" not in r]),
                    "agents_failed": len([r for r in results.values() if "error" in r])
                }
            )
        
        logger.info(f"Completed training all agents in {overall_training_time:.1f}s")
        
        return {
            "experiment_id": self.current_experiment_id,
            "overall_training_time": overall_training_time,
            "results": results,
            "summary": self._generate_training_summary(results)
        }
    
    def evaluate_policies(self, 
                         agent_types: List[str] = None,
                         num_episodes: int = 100) -> Dict[str, Any]:
        """Evaluate trained policies."""
        if agent_types is None:
            agent_types = ["knowledge_agent", "code_generation_agent"]
        
        evaluation_results = {}
        
        for agent_type in agent_types:
            # Get best model for agent
            best_model_path = self.policy_optimizer.get_best_model(agent_type)
            if not best_model_path:
                logger.warning(f"No trained model found for {agent_type}")
                evaluation_results[agent_type] = {"error": "No trained model found"}
                continue
            
            try:
                # Load and evaluate model
                eval_metrics = self._evaluate_model(best_model_path, num_episodes)
                evaluation_results[agent_type] = eval_metrics
                logger.info(f"Evaluated {agent_type}: success_rate={eval_metrics['success_rate']:.3f}")
            except Exception as e:
                logger.error(f"Failed to evaluate {agent_type}: {e}")
                evaluation_results[agent_type] = {"error": str(e)}
        
        return evaluation_results
    
    def continuous_optimization(self, 
                               check_interval: int = 3600,
                               min_new_trajectories: int = 50) -> None:
        """Run continuous optimization loop."""
        logger.info("Starting continuous optimization loop")
        
        last_optimization_time = time.time()
        last_trajectory_count = len(self.trajectory_collector.completed_trajectories)
        
        while True:
            try:
                time.sleep(check_interval)
                
                # Check for new trajectories
                current_trajectory_count = len(self.trajectory_collector.completed_trajectories)
                new_trajectories = current_trajectory_count - last_trajectory_count
                
                if new_trajectories >= min_new_trajectories:
                    logger.info(f"Found {new_trajectories} new trajectories, starting optimization")
                    
                    # Run optimization for all agents
                    results = self.train_all_agents()
                    
                    # Update tracking variables
                    last_optimization_time = time.time()
                    last_trajectory_count = current_trajectory_count
                    
                    logger.info("Continuous optimization cycle completed")
                else:
                    logger.debug(f"Only {new_trajectories} new trajectories, waiting for more data")
                
            except KeyboardInterrupt:
                logger.info("Continuous optimization stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in continuous optimization: {e}")
                time.sleep(60)  # Wait before retrying
    
    def _register_trained_model(self, 
                               agent_type: str,
                               optimization_result) -> str:
        """Register trained model in model registry."""
        model_name = f"{agent_type}_rl_policy"
        
        # Create version based on performance
        performance_score = int(optimization_result.best_reward * 100)
        version = f"1.{performance_score}.0"
        
        model_id = self.model_registry.register_model(
            name=model_name,
            model_type=ModelType.REINFORCEMENT_LEARNING,
            version=version,
            description=f"RL policy for {agent_type} trained with {self.config.algorithm.value}",
            framework="ray_rllib",
            architecture=self.config.algorithm.value,
            model_path=optimization_result.checkpoint_path,
            training_parameters=optimization_result.hyperparameters,
            performance_metrics={
                "best_reward": optimization_result.best_reward,
                "final_reward": optimization_result.final_reward,
                "success_rate": optimization_result.success_rate,
                "training_time": optimization_result.training_time
            },
            tags=[agent_type, "rl_policy", self.config.algorithm.value]
        )
        
        # Update model status to production if performance is good
        if optimization_result.success_rate > 0.7:
            self.model_registry.update_model(model_id, status=ModelStatus.PRODUCTION)
        
        logger.info(f"Registered model {model_name} v{version} with ID: {model_id}")
        
        return model_id
    
    def _evaluate_model(self, model_path: str, num_episodes: int) -> Dict[str, float]:
        """Evaluate a trained model."""
        # This would load the model and run evaluation
        # For now, return mock metrics
        return {
            "avg_reward": 0.75,
            "success_rate": 0.80,
            "avg_episode_length": 15.5,
            "std_reward": 0.15
        }
    
    def _generate_training_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of training results."""
        successful_agents = [agent for agent, result in results.items() if "error" not in result]
        failed_agents = [agent for agent, result in results.items() if "error" in result]
        
        if successful_agents:
            avg_reward = sum(
                results[agent]["optimization_result"]["best_reward"] 
                for agent in successful_agents
            ) / len(successful_agents)
            
            avg_success_rate = sum(
                results[agent]["optimization_result"]["success_rate"]
                for agent in successful_agents
            ) / len(successful_agents)
        else:
            avg_reward = 0.0
            avg_success_rate = 0.0
        
        return {
            "total_agents": len(results),
            "successful_agents": len(successful_agents),
            "failed_agents": len(failed_agents),
            "avg_reward": avg_reward,
            "avg_success_rate": avg_success_rate,
            "success_rate": len(successful_agents) / len(results) if results else 0.0
        }
    
    def get_training_statistics(self) -> Dict[str, Any]:
        """Get comprehensive training statistics."""
        trajectory_stats = self.trajectory_collector.get_statistics()
        
        return {
            "trajectory_statistics": trajectory_stats,
            "training_history": len(self.training_history),
            "optimization_history": len(self.policy_optimizer.optimization_history),
            "workspace_dir": str(self.workspace_dir),
            "config": self.config.__dict__
        }
    
    def export_training_data(self, output_path: str) -> str:
        """Export all training data for analysis."""
        export_data = {
            "metadata": {
                "export_time": datetime.now().isoformat(),
                "config": self.config.__dict__
            },
            "trajectory_statistics": self.trajectory_collector.get_statistics(),
            "training_history": self.training_history,
            "optimization_history": [r.to_dict() for r in self.policy_optimizer.optimization_history]
        }
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        logger.info(f"Exported training data to {output_path}")
        return str(output_file)

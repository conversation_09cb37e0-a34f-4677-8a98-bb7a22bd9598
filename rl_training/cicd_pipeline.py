"""
CI/CD Pipeline for Automated RL Model Updates

This module provides continuous integration and deployment capabilities
for RL model training, validation, and deployment automation.
"""

import json
import logging
import time
import schedule
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import subprocess
import yaml

from .rl_trainer import R<PERSON>rainer
from .training_config import TrainingConfig, TrainingMode, OptimizationConfig
from .trajectory_collector import TrajectoryCollector

logger = logging.getLogger(__name__)


class CICDPipeline:
    """CI/CD pipeline for automated RL model updates."""
    
    def __init__(self, 
                 config_path: str = "rl_training/cicd_config.yaml",
                 workspace_dir: str = "rl_cicd_workspace"):
        """Initialize CI/CD pipeline."""
        self.config_path = Path(config_path)
        self.workspace_dir = Path(workspace_dir)
        self.workspace_dir.mkdir(parents=True, exist_ok=True)
        
        # Load configuration
        self.config = self._load_config()
        
        # Initialize components
        self.trainer = None
        self.last_update_time = datetime.now()
        self.pipeline_history: List[Dict[str, Any]] = []
        
        # Performance tracking
        self.baseline_metrics: Dict[str, float] = {}
        self.current_metrics: Dict[str, float] = {}
        
        logger.info(f"CI/CD Pipeline initialized with config: {config_path}")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load CI/CD configuration."""
        default_config = {
            "pipeline": {
                "check_interval_hours": 6,
                "min_new_trajectories": 100,
                "performance_threshold": 0.05,
                "auto_deploy": False,
                "max_concurrent_training": 2
            },
            "training": {
                "algorithm": "ppo",
                "max_iterations": 200,
                "early_stopping": True,
                "validation_episodes": 100
            },
            "deployment": {
                "staging_validation": True,
                "rollback_on_failure": True,
                "gradual_rollout": True,
                "monitoring_period_hours": 24
            },
            "notifications": {
                "enabled": False,
                "webhook_url": None,
                "email_recipients": []
            }
        }
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    user_config = yaml.safe_load(f)
                
                # Merge with defaults
                default_config.update(user_config)
                logger.info(f"Loaded CI/CD config from {self.config_path}")
            except Exception as e:
                logger.warning(f"Failed to load config from {self.config_path}: {e}")
                logger.info("Using default configuration")
        else:
            logger.info("Using default CI/CD configuration")
        
        return default_config
    
    def initialize_trainer(self, training_config: TrainingConfig = None) -> None:
        """Initialize RL trainer with configuration."""
        if training_config is None:
            training_config = TrainingConfig(
                experiment_name="cicd_automated_training",
                training_mode=TrainingMode.ONLINE,
                max_training_iterations=self.config["training"]["max_iterations"],
                track_experiments=True,
                register_models=True,
                auto_deploy=self.config["pipeline"]["auto_deploy"]
            )
        
        self.trainer = RLTrainer(
            config=training_config,
            workspace_dir=str(self.workspace_dir / "training")
        )
        
        # Load baseline metrics if available
        self._load_baseline_metrics()
        
        logger.info("RL trainer initialized for CI/CD pipeline")
    
    def start_continuous_pipeline(self) -> None:
        """Start continuous CI/CD pipeline."""
        if not self.trainer:
            self.initialize_trainer()
        
        check_interval = self.config["pipeline"]["check_interval_hours"]
        
        # Schedule pipeline checks
        schedule.every(check_interval).hours.do(self._run_pipeline_check)
        
        # Schedule daily health checks
        schedule.every().day.at("02:00").do(self._run_health_check)
        
        # Schedule weekly baseline updates
        schedule.every().sunday.at("03:00").do(self._update_baseline_metrics)
        
        logger.info(f"Started continuous CI/CD pipeline (check interval: {check_interval}h)")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            logger.info("CI/CD pipeline stopped by user")
        except Exception as e:
            logger.error(f"CI/CD pipeline error: {e}")
            self._send_notification("error", f"Pipeline error: {e}")
    
    def _run_pipeline_check(self) -> Dict[str, Any]:
        """Run automated pipeline check."""
        pipeline_id = f"pipeline_{int(time.time())}"
        start_time = datetime.now()
        
        logger.info(f"Starting pipeline check: {pipeline_id}")
        
        try:
            # Step 1: Check for new trajectory data
            data_check = self._check_new_data()
            if not data_check["sufficient_data"]:
                logger.info(f"Insufficient new data: {data_check['new_trajectories']} < {self.config['pipeline']['min_new_trajectories']}")
                return self._create_pipeline_result(pipeline_id, "skipped", "Insufficient new data", start_time)
            
            # Step 2: Train new models
            training_results = self._run_automated_training()
            if not training_results["success"]:
                logger.error(f"Training failed: {training_results['error']}")
                return self._create_pipeline_result(pipeline_id, "failed", training_results["error"], start_time)
            
            # Step 3: Validate new models
            validation_results = self._validate_models(training_results["models"])
            if not validation_results["passed"]:
                logger.warning(f"Model validation failed: {validation_results['reason']}")
                return self._create_pipeline_result(pipeline_id, "validation_failed", validation_results["reason"], start_time)
            
            # Step 4: Deploy if auto-deploy is enabled
            deployment_results = {"deployed": False, "reason": "Auto-deploy disabled"}
            if self.config["pipeline"]["auto_deploy"]:
                deployment_results = self._deploy_models(training_results["models"])
            
            # Step 5: Update metrics and send notifications
            self._update_current_metrics(validation_results["metrics"])
            self._send_notification("success", f"Pipeline {pipeline_id} completed successfully")
            
            result = self._create_pipeline_result(
                pipeline_id, "success", "Pipeline completed successfully", start_time,
                {
                    "data_check": data_check,
                    "training": training_results,
                    "validation": validation_results,
                    "deployment": deployment_results
                }
            )
            
            self.pipeline_history.append(result)
            self.last_update_time = datetime.now()
            
            logger.info(f"Pipeline check completed: {pipeline_id}")
            return result
            
        except Exception as e:
            logger.error(f"Pipeline check failed: {e}")
            self._send_notification("error", f"Pipeline {pipeline_id} failed: {e}")
            return self._create_pipeline_result(pipeline_id, "error", str(e), start_time)
    
    def _check_new_data(self) -> Dict[str, Any]:
        """Check for sufficient new trajectory data."""
        collector = self.trainer.trajectory_collector
        stats = collector.get_statistics()
        
        # Calculate new trajectories since last update
        time_threshold = self.last_update_time
        new_trajectories = 0
        
        for trajectory in collector.completed_trajectories:
            if trajectory.start_time > time_threshold:
                new_trajectories += 1
        
        sufficient_data = new_trajectories >= self.config["pipeline"]["min_new_trajectories"]
        
        return {
            "new_trajectories": new_trajectories,
            "total_trajectories": stats["total_trajectories"],
            "sufficient_data": sufficient_data,
            "threshold": self.config["pipeline"]["min_new_trajectories"]
        }
    
    def _run_automated_training(self) -> Dict[str, Any]:
        """Run automated training for all agent types."""
        try:
            optimization_config = OptimizationConfig(
                max_iterations=self.config["training"]["max_iterations"],
                early_stopping=self.config["training"]["early_stopping"],
                eval_episodes=self.config["training"]["validation_episodes"]
            )
            
            # Train all agents
            results = self.trainer.train_all_agents(
                agent_types=["knowledge_agent", "code_generation_agent"],
                optimization_config=optimization_config
            )
            
            # Extract model information
            models = {}
            for agent_type, result in results["results"].items():
                if "error" not in result:
                    models[agent_type] = {
                        "model_id": result.get("model_id"),
                        "optimization_result": result["optimization_result"]
                    }
            
            return {
                "success": True,
                "models": models,
                "training_time": results["overall_training_time"],
                "experiment_id": results["experiment_id"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "models": {}
            }
    
    def _validate_models(self, models: Dict[str, Any]) -> Dict[str, Any]:
        """Validate trained models against performance thresholds."""
        try:
            # Run evaluation
            agent_types = list(models.keys())
            evaluation_results = self.trainer.evaluate_policies(
                agent_types=agent_types,
                num_episodes=self.config["training"]["validation_episodes"]
            )
            
            # Check performance improvements
            performance_threshold = self.config["pipeline"]["performance_threshold"]
            validation_passed = True
            validation_details = {}
            
            for agent_type, eval_result in evaluation_results.items():
                if "error" in eval_result:
                    validation_passed = False
                    validation_details[agent_type] = {"error": eval_result["error"]}
                    continue
                
                # Compare with baseline
                baseline_key = f"{agent_type}_success_rate"
                current_success_rate = eval_result["success_rate"]
                baseline_success_rate = self.baseline_metrics.get(baseline_key, 0.0)
                
                improvement = current_success_rate - baseline_success_rate
                meets_threshold = improvement >= performance_threshold
                
                validation_details[agent_type] = {
                    "current_success_rate": current_success_rate,
                    "baseline_success_rate": baseline_success_rate,
                    "improvement": improvement,
                    "meets_threshold": meets_threshold
                }
                
                if not meets_threshold:
                    validation_passed = False
            
            return {
                "passed": validation_passed,
                "metrics": evaluation_results,
                "details": validation_details,
                "reason": "Performance threshold not met" if not validation_passed else "Validation passed"
            }
            
        except Exception as e:
            return {
                "passed": False,
                "metrics": {},
                "details": {},
                "reason": f"Validation error: {e}"
            }
    
    def _deploy_models(self, models: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy validated models."""
        if not self.config["deployment"]["staging_validation"]:
            # Direct deployment
            return self._direct_deploy(models)
        else:
            # Staged deployment with validation
            return self._staged_deploy(models)
    
    def _direct_deploy(self, models: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy models directly to production."""
        deployed_models = {}
        
        for agent_type, model_info in models.items():
            try:
                # Update model status to production
                if self.trainer.model_registry and model_info.get("model_id"):
                    self.trainer.model_registry.update_model(
                        model_info["model_id"],
                        status="production",
                        deployment_time=datetime.now()
                    )
                
                deployed_models[agent_type] = {
                    "deployed": True,
                    "model_id": model_info.get("model_id"),
                    "deployment_time": datetime.now().isoformat()
                }
                
            except Exception as e:
                deployed_models[agent_type] = {
                    "deployed": False,
                    "error": str(e)
                }
        
        return {
            "deployed": True,
            "strategy": "direct",
            "models": deployed_models
        }
    
    def _staged_deploy(self, models: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy models with staging validation."""
        # For now, implement as direct deploy
        # In a real system, this would involve:
        # 1. Deploy to staging environment
        # 2. Run validation tests
        # 3. Gradual rollout to production
        # 4. Monitor performance
        
        return self._direct_deploy(models)
    
    def _run_health_check(self) -> Dict[str, Any]:
        """Run daily health check."""
        logger.info("Running daily health check")
        
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "trajectory_collection": self._check_trajectory_collection_health(),
            "model_performance": self._check_model_performance_health(),
            "storage_usage": self._check_storage_health(),
            "pipeline_status": self._check_pipeline_health()
        }
        
        # Send notification if issues found
        issues = [k for k, v in health_status.items() 
                 if isinstance(v, dict) and not v.get("healthy", True)]
        
        if issues:
            self._send_notification("warning", f"Health check issues: {', '.join(issues)}")
        
        return health_status
    
    def _check_trajectory_collection_health(self) -> Dict[str, Any]:
        """Check trajectory collection health."""
        if not self.trainer:
            return {"healthy": False, "reason": "Trainer not initialized"}
        
        stats = self.trainer.trajectory_collector.get_statistics()
        
        # Check if we're collecting new data
        recent_threshold = datetime.now() - timedelta(hours=24)
        recent_trajectories = 0
        
        for trajectory in self.trainer.trajectory_collector.completed_trajectories:
            if trajectory.start_time > recent_threshold:
                recent_trajectories += 1
        
        healthy = recent_trajectories > 0
        
        return {
            "healthy": healthy,
            "recent_trajectories": recent_trajectories,
            "total_trajectories": stats["total_trajectories"],
            "success_rate": stats["success_rate"]
        }
    
    def _check_model_performance_health(self) -> Dict[str, Any]:
        """Check model performance health."""
        # Compare current metrics with baseline
        performance_degradation = False
        degradation_details = {}
        
        for metric, current_value in self.current_metrics.items():
            baseline_value = self.baseline_metrics.get(metric, current_value)
            if current_value < baseline_value * 0.9:  # 10% degradation threshold
                performance_degradation = True
                degradation_details[metric] = {
                    "current": current_value,
                    "baseline": baseline_value,
                    "degradation": (baseline_value - current_value) / baseline_value
                }
        
        return {
            "healthy": not performance_degradation,
            "degradation_details": degradation_details,
            "current_metrics": self.current_metrics,
            "baseline_metrics": self.baseline_metrics
        }
    
    def _check_storage_health(self) -> Dict[str, Any]:
        """Check storage usage health."""
        try:
            # Check workspace directory size
            total_size = sum(f.stat().st_size for f in self.workspace_dir.rglob('*') if f.is_file())
            size_mb = total_size / (1024 * 1024)
            
            # Simple threshold check (1GB)
            healthy = size_mb < 1024
            
            return {
                "healthy": healthy,
                "size_mb": size_mb,
                "workspace_dir": str(self.workspace_dir)
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e)
            }
    
    def _check_pipeline_health(self) -> Dict[str, Any]:
        """Check pipeline execution health."""
        if not self.pipeline_history:
            return {"healthy": True, "reason": "No pipeline history"}
        
        # Check recent pipeline success rate
        recent_pipelines = [p for p in self.pipeline_history[-10:]]  # Last 10 runs
        successful_runs = [p for p in recent_pipelines if p["status"] == "success"]
        
        success_rate = len(successful_runs) / len(recent_pipelines)
        healthy = success_rate >= 0.8  # 80% success rate threshold
        
        return {
            "healthy": healthy,
            "recent_success_rate": success_rate,
            "total_runs": len(self.pipeline_history),
            "recent_runs": len(recent_pipelines)
        }
    
    def _update_baseline_metrics(self) -> None:
        """Update baseline performance metrics."""
        logger.info("Updating baseline metrics")
        
        if self.current_metrics:
            self.baseline_metrics.update(self.current_metrics)
            
            # Save to file
            baseline_file = self.workspace_dir / "baseline_metrics.json"
            with open(baseline_file, 'w') as f:
                json.dump(self.baseline_metrics, f, indent=2)
            
            logger.info(f"Updated baseline metrics: {len(self.baseline_metrics)} metrics")
    
    def _load_baseline_metrics(self) -> None:
        """Load baseline metrics from file."""
        baseline_file = self.workspace_dir / "baseline_metrics.json"
        
        if baseline_file.exists():
            try:
                with open(baseline_file, 'r') as f:
                    self.baseline_metrics = json.load(f)
                logger.info(f"Loaded baseline metrics: {len(self.baseline_metrics)} metrics")
            except Exception as e:
                logger.warning(f"Failed to load baseline metrics: {e}")
    
    def _update_current_metrics(self, metrics: Dict[str, Any]) -> None:
        """Update current performance metrics."""
        for agent_type, agent_metrics in metrics.items():
            if "error" not in agent_metrics:
                self.current_metrics[f"{agent_type}_success_rate"] = agent_metrics["success_rate"]
                self.current_metrics[f"{agent_type}_avg_reward"] = agent_metrics["avg_reward"]
    
    def _send_notification(self, level: str, message: str) -> None:
        """Send notification about pipeline events."""
        if not self.config["notifications"]["enabled"]:
            return
        
        notification = {
            "timestamp": datetime.now().isoformat(),
            "level": level,
            "message": message,
            "pipeline": "rl_training_cicd"
        }
        
        logger.info(f"Notification ({level}): {message}")
        
        # In a real system, this would send to webhook/email
        # For now, just log the notification
    
    def _create_pipeline_result(self, 
                               pipeline_id: str,
                               status: str,
                               message: str,
                               start_time: datetime,
                               details: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create pipeline execution result."""
        return {
            "pipeline_id": pipeline_id,
            "status": status,
            "message": message,
            "start_time": start_time.isoformat(),
            "end_time": datetime.now().isoformat(),
            "duration": (datetime.now() - start_time).total_seconds(),
            "details": details or {}
        }
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status."""
        return {
            "last_update": self.last_update_time.isoformat(),
            "pipeline_runs": len(self.pipeline_history),
            "recent_runs": self.pipeline_history[-5:] if self.pipeline_history else [],
            "current_metrics": self.current_metrics,
            "baseline_metrics": self.baseline_metrics,
            "config": self.config
        }

"""
Trajectory Data Collector for RL Training

This module collects and manages trajectory data from agent interactions
for offline RL training and strategy optimization.
"""

import json
import time
import uuid
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import numpy as np
import logging

logger = logging.getLogger(__name__)


@dataclass
class ActionData:
    """Data structure for a single action in a trajectory."""
    action_id: str
    action_type: str  # tool_selection, strategy_selection, etc.
    action_value: Union[int, str, Dict[str, Any]]
    timestamp: float
    context: Dict[str, Any]
    metadata: Dict[str, Any] = None


@dataclass
class StateData:
    """Data structure for environment state."""
    state_id: str
    observation: List[float]
    task_type: str
    task_progress: float
    context_complexity: float
    timestamp: float
    metadata: Dict[str, Any] = None


@dataclass
class RewardData:
    """Data structure for reward information."""
    reward_value: float
    reward_components: Dict[str, float]
    success: bool
    timestamp: float
    metadata: Dict[str, Any] = None


@dataclass
class TrajectoryData:
    """Complete trajectory data structure."""
    trajectory_id: str
    agent_type: str  # knowledge_agent, code_generation_agent
    start_time: datetime
    end_time: Optional[datetime]
    
    # Trajectory components
    states: List[StateData]
    actions: List[ActionData]
    rewards: List[RewardData]
    
    # Trajectory metadata
    total_reward: float
    success: bool
    episode_length: int
    task_completed: bool
    
    # Additional context
    initial_context: Dict[str, Any]
    final_context: Dict[str, Any]
    error_messages: List[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert trajectory to dictionary for serialization."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TrajectoryData':
        """Create trajectory from dictionary."""
        # Convert datetime strings back to datetime objects
        data['start_time'] = datetime.fromisoformat(data['start_time'])
        if data['end_time']:
            data['end_time'] = datetime.fromisoformat(data['end_time'])
        
        # Convert nested data structures
        data['states'] = [StateData(**state) for state in data['states']]
        data['actions'] = [ActionData(**action) for action in data['actions']]
        data['rewards'] = [RewardData(**reward) for reward in data['rewards']]
        
        return cls(**data)


class TrajectoryCollector:
    """Collects and manages trajectory data for RL training."""
    
    def __init__(self, 
                 storage_path: str = "rl_training_data/trajectories",
                 max_trajectories: int = 10000,
                 auto_save: bool = True):
        """Initialize trajectory collector."""
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.max_trajectories = max_trajectories
        self.auto_save = auto_save
        
        # Active trajectories being collected
        self.active_trajectories: Dict[str, TrajectoryData] = {}
        
        # Completed trajectories
        self.completed_trajectories: List[TrajectoryData] = []
        
        # Load existing trajectories
        self._load_existing_trajectories()
        
        logger.info(f"TrajectoryCollector initialized with {len(self.completed_trajectories)} existing trajectories")
    
    def start_trajectory(self, 
                        agent_type: str,
                        initial_context: Dict[str, Any]) -> str:
        """Start collecting a new trajectory."""
        trajectory_id = str(uuid.uuid4())
        
        trajectory = TrajectoryData(
            trajectory_id=trajectory_id,
            agent_type=agent_type,
            start_time=datetime.now(),
            end_time=None,
            states=[],
            actions=[],
            rewards=[],
            total_reward=0.0,
            success=False,
            episode_length=0,
            task_completed=False,
            initial_context=initial_context,
            final_context={},
            error_messages=[]
        )
        
        self.active_trajectories[trajectory_id] = trajectory
        logger.debug(f"Started trajectory {trajectory_id} for {agent_type}")
        
        return trajectory_id
    
    def add_state(self, 
                  trajectory_id: str,
                  observation: List[float],
                  task_type: str,
                  task_progress: float,
                  context_complexity: float,
                  metadata: Dict[str, Any] = None) -> str:
        """Add state data to trajectory."""
        if trajectory_id not in self.active_trajectories:
            raise ValueError(f"Trajectory {trajectory_id} not found")
        
        state_id = str(uuid.uuid4())
        state = StateData(
            state_id=state_id,
            observation=observation,
            task_type=task_type,
            task_progress=task_progress,
            context_complexity=context_complexity,
            timestamp=time.time(),
            metadata=metadata or {}
        )
        
        self.active_trajectories[trajectory_id].states.append(state)
        return state_id
    
    def add_action(self,
                   trajectory_id: str,
                   action_type: str,
                   action_value: Union[int, str, Dict[str, Any]],
                   context: Dict[str, Any],
                   metadata: Dict[str, Any] = None) -> str:
        """Add action data to trajectory."""
        if trajectory_id not in self.active_trajectories:
            raise ValueError(f"Trajectory {trajectory_id} not found")
        
        action_id = str(uuid.uuid4())
        action = ActionData(
            action_id=action_id,
            action_type=action_type,
            action_value=action_value,
            timestamp=time.time(),
            context=context,
            metadata=metadata or {}
        )
        
        self.active_trajectories[trajectory_id].actions.append(action)
        return action_id
    
    def add_reward(self,
                   trajectory_id: str,
                   reward_value: float,
                   reward_components: Dict[str, float],
                   success: bool,
                   metadata: Dict[str, Any] = None) -> None:
        """Add reward data to trajectory."""
        if trajectory_id not in self.active_trajectories:
            raise ValueError(f"Trajectory {trajectory_id} not found")
        
        reward = RewardData(
            reward_value=reward_value,
            reward_components=reward_components,
            success=success,
            timestamp=time.time(),
            metadata=metadata or {}
        )
        
        trajectory = self.active_trajectories[trajectory_id]
        trajectory.rewards.append(reward)
        trajectory.total_reward += reward_value
    
    def end_trajectory(self,
                       trajectory_id: str,
                       success: bool,
                       task_completed: bool,
                       final_context: Dict[str, Any],
                       error_messages: List[str] = None) -> TrajectoryData:
        """End trajectory collection and finalize data."""
        if trajectory_id not in self.active_trajectories:
            raise ValueError(f"Trajectory {trajectory_id} not found")
        
        trajectory = self.active_trajectories[trajectory_id]
        trajectory.end_time = datetime.now()
        trajectory.success = success
        trajectory.task_completed = task_completed
        trajectory.final_context = final_context
        trajectory.episode_length = len(trajectory.actions)
        trajectory.error_messages = error_messages or []
        
        # Move to completed trajectories
        self.completed_trajectories.append(trajectory)
        del self.active_trajectories[trajectory_id]
        
        # Auto-save if enabled
        if self.auto_save:
            self._save_trajectory(trajectory)
        
        # Manage storage limits
        self._manage_storage_limits()
        
        logger.info(f"Completed trajectory {trajectory_id}: success={success}, reward={trajectory.total_reward:.2f}")
        
        return trajectory
    
    def get_trajectories(self,
                        agent_type: Optional[str] = None,
                        success_only: bool = False,
                        min_reward: Optional[float] = None,
                        max_trajectories: Optional[int] = None) -> List[TrajectoryData]:
        """Get trajectories with optional filtering."""
        trajectories = self.completed_trajectories.copy()
        
        # Apply filters
        if agent_type:
            trajectories = [t for t in trajectories if t.agent_type == agent_type]
        
        if success_only:
            trajectories = [t for t in trajectories if t.success]
        
        if min_reward is not None:
            trajectories = [t for t in trajectories if t.total_reward >= min_reward]
        
        # Sort by total reward (descending)
        trajectories.sort(key=lambda t: t.total_reward, reverse=True)
        
        # Limit number of trajectories
        if max_trajectories:
            trajectories = trajectories[:max_trajectories]
        
        return trajectories
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get trajectory collection statistics."""
        if not self.completed_trajectories:
            return {"total_trajectories": 0}
        
        rewards = [t.total_reward for t in self.completed_trajectories]
        success_count = sum(1 for t in self.completed_trajectories if t.success)
        
        agent_types = {}
        for trajectory in self.completed_trajectories:
            agent_type = trajectory.agent_type
            if agent_type not in agent_types:
                agent_types[agent_type] = {"count": 0, "success": 0, "avg_reward": 0.0}
            
            agent_types[agent_type]["count"] += 1
            if trajectory.success:
                agent_types[agent_type]["success"] += 1
            agent_types[agent_type]["avg_reward"] += trajectory.total_reward
        
        # Calculate averages
        for agent_type in agent_types:
            count = agent_types[agent_type]["count"]
            agent_types[agent_type]["success_rate"] = agent_types[agent_type]["success"] / count
            agent_types[agent_type]["avg_reward"] /= count
        
        return {
            "total_trajectories": len(self.completed_trajectories),
            "success_rate": success_count / len(self.completed_trajectories),
            "avg_reward": np.mean(rewards),
            "min_reward": np.min(rewards),
            "max_reward": np.max(rewards),
            "std_reward": np.std(rewards),
            "agent_types": agent_types,
            "active_trajectories": len(self.active_trajectories)
        }
    
    def _save_trajectory(self, trajectory: TrajectoryData) -> None:
        """Save individual trajectory to file."""
        filename = f"trajectory_{trajectory.trajectory_id}.json"
        filepath = self.storage_path / filename
        
        # Convert to dict and handle datetime serialization
        data = trajectory.to_dict()
        data['start_time'] = trajectory.start_time.isoformat()
        if trajectory.end_time:
            data['end_time'] = trajectory.end_time.isoformat()
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
    
    def _load_existing_trajectories(self) -> None:
        """Load existing trajectories from storage."""
        if not self.storage_path.exists():
            return
        
        for filepath in self.storage_path.glob("trajectory_*.json"):
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)
                
                trajectory = TrajectoryData.from_dict(data)
                self.completed_trajectories.append(trajectory)
                
            except Exception as e:
                logger.warning(f"Failed to load trajectory from {filepath}: {e}")
    
    def _manage_storage_limits(self) -> None:
        """Manage storage limits by removing oldest trajectories."""
        if len(self.completed_trajectories) > self.max_trajectories:
            # Sort by start time and remove oldest
            self.completed_trajectories.sort(key=lambda t: t.start_time)

            # Remove excess trajectories
            excess = len(self.completed_trajectories) - self.max_trajectories
            removed_trajectories = self.completed_trajectories[:excess]
            self.completed_trajectories = self.completed_trajectories[excess:]

            # Remove files
            for trajectory in removed_trajectories:
                filepath = self.storage_path / f"trajectory_{trajectory.trajectory_id}.json"
                if filepath.exists():
                    filepath.unlink()

            logger.info(f"Removed {excess} old trajectories to maintain storage limits")

    def export_for_training(self,
                           output_path: str,
                           format: str = "json") -> str:
        """Export trajectories in format suitable for RL training."""
        export_data = {
            "metadata": {
                "export_time": datetime.now().isoformat(),
                "total_trajectories": len(self.completed_trajectories),
                "statistics": self.get_statistics()
            },
            "trajectories": []
        }

        for trajectory in self.completed_trajectories:
            trajectory_dict = trajectory.to_dict()
            trajectory_dict['start_time'] = trajectory.start_time.isoformat()
            if trajectory.end_time:
                trajectory_dict['end_time'] = trajectory.end_time.isoformat()
            export_data["trajectories"].append(trajectory_dict)

        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        with open(output_file, 'w') as f:
            json.dump(export_data, f, indent=2)

        logger.info(f"Exported {len(self.completed_trajectories)} trajectories to {output_path}")
        return str(output_file)

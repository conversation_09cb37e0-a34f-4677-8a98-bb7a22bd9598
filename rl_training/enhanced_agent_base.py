"""
Enhanced Agent Base Class with Trajectory Collection

This module provides a base class for agents that automatically collect
trajectory data for RL training and optimization.
"""

import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

from .trajectory_collector import TrajectoryCollector

logger = logging.getLogger(__name__)


@dataclass
class AgentAction:
    """Represents an action taken by an agent."""
    action_type: str
    action_value: Union[int, str, Dict[str, Any]]
    context: Dict[str, Any]
    timestamp: float
    metadata: Dict[str, Any] = None


@dataclass
class AgentState:
    """Represents the state of an agent."""
    observation: List[float]
    task_type: str
    task_progress: float
    context_complexity: float
    timestamp: float
    metadata: Dict[str, Any] = None


class EnhancedAgentBase(ABC):
    """Base class for agents with trajectory collection capabilities."""
    
    def __init__(self, 
                 agent_type: str,
                 trajectory_collector: Optional[TrajectoryCollector] = None,
                 enable_trajectory_collection: bool = True):
        """Initialize enhanced agent base."""
        self.agent_type = agent_type
        self.enable_trajectory_collection = enable_trajectory_collection
        
        # Trajectory collection
        if enable_trajectory_collection and trajectory_collector:
            self.trajectory_collector = trajectory_collector
        else:
            self.trajectory_collector = None
        
        # Current trajectory tracking
        self.current_trajectory_id: Optional[str] = None
        self.trajectory_start_time: Optional[float] = None
        self.trajectory_context: Dict[str, Any] = {}
        
        # Performance tracking
        self.action_history: List[AgentAction] = []
        self.state_history: List[AgentState] = []
        self.reward_history: List[Dict[str, Any]] = []
        
        logger.debug(f"Enhanced agent {agent_type} initialized with trajectory collection: {enable_trajectory_collection}")
    
    def start_trajectory(self, initial_context: Dict[str, Any]) -> Optional[str]:
        """Start a new trajectory for data collection."""
        if not self.enable_trajectory_collection or not self.trajectory_collector:
            return None
        
        self.current_trajectory_id = self.trajectory_collector.start_trajectory(
            agent_type=self.agent_type,
            initial_context=initial_context
        )
        
        self.trajectory_start_time = time.time()
        self.trajectory_context = initial_context.copy()
        
        # Clear history for new trajectory
        self.action_history.clear()
        self.state_history.clear()
        self.reward_history.clear()
        
        logger.debug(f"Started trajectory {self.current_trajectory_id}")
        return self.current_trajectory_id
    
    def record_state(self,
                    observation: List[float],
                    task_type: str,
                    task_progress: float,
                    context_complexity: float,
                    metadata: Dict[str, Any] = None) -> None:
        """Record current agent state."""
        state = AgentState(
            observation=observation,
            task_type=task_type,
            task_progress=task_progress,
            context_complexity=context_complexity,
            timestamp=time.time(),
            metadata=metadata or {}
        )
        
        self.state_history.append(state)
        
        # Add to trajectory if collection is enabled
        if (self.enable_trajectory_collection and 
            self.trajectory_collector and 
            self.current_trajectory_id):
            
            self.trajectory_collector.add_state(
                trajectory_id=self.current_trajectory_id,
                observation=observation,
                task_type=task_type,
                task_progress=task_progress,
                context_complexity=context_complexity,
                metadata=metadata
            )
    
    def record_action(self,
                     action_type: str,
                     action_value: Union[int, str, Dict[str, Any]],
                     context: Dict[str, Any],
                     metadata: Dict[str, Any] = None) -> None:
        """Record an action taken by the agent."""
        action = AgentAction(
            action_type=action_type,
            action_value=action_value,
            context=context,
            timestamp=time.time(),
            metadata=metadata or {}
        )
        
        self.action_history.append(action)
        
        # Add to trajectory if collection is enabled
        if (self.enable_trajectory_collection and 
            self.trajectory_collector and 
            self.current_trajectory_id):
            
            self.trajectory_collector.add_action(
                trajectory_id=self.current_trajectory_id,
                action_type=action_type,
                action_value=action_value,
                context=context,
                metadata=metadata
            )
    
    def record_reward(self,
                     reward_value: float,
                     reward_components: Dict[str, float],
                     success: bool,
                     metadata: Dict[str, Any] = None) -> None:
        """Record reward received for actions."""
        reward_data = {
            "reward_value": reward_value,
            "reward_components": reward_components,
            "success": success,
            "timestamp": time.time(),
            "metadata": metadata or {}
        }
        
        self.reward_history.append(reward_data)
        
        # Add to trajectory if collection is enabled
        if (self.enable_trajectory_collection and 
            self.trajectory_collector and 
            self.current_trajectory_id):
            
            self.trajectory_collector.add_reward(
                trajectory_id=self.current_trajectory_id,
                reward_value=reward_value,
                reward_components=reward_components,
                success=success,
                metadata=metadata
            )
    
    def end_trajectory(self,
                      success: bool,
                      task_completed: bool,
                      final_context: Dict[str, Any] = None,
                      error_messages: List[str] = None) -> Optional[str]:
        """End current trajectory and finalize data collection."""
        if (not self.enable_trajectory_collection or 
            not self.trajectory_collector or 
            not self.current_trajectory_id):
            return None
        
        trajectory_id = self.current_trajectory_id
        
        # Finalize trajectory
        self.trajectory_collector.end_trajectory(
            trajectory_id=trajectory_id,
            success=success,
            task_completed=task_completed,
            final_context=final_context or self.trajectory_context,
            error_messages=error_messages
        )
        
        # Calculate trajectory metrics
        trajectory_duration = time.time() - self.trajectory_start_time if self.trajectory_start_time else 0
        total_reward = sum(r["reward_value"] for r in self.reward_history)
        
        logger.info(f"Ended trajectory {trajectory_id}: success={success}, "
                   f"duration={trajectory_duration:.2f}s, total_reward={total_reward:.2f}")
        
        # Reset trajectory tracking
        self.current_trajectory_id = None
        self.trajectory_start_time = None
        self.trajectory_context.clear()
        
        return trajectory_id
    
    def get_current_state_vector(self) -> List[float]:
        """Get current state as vector for RL algorithms."""
        if not self.state_history:
            return [0.0] * 6  # Default state vector size
        
        latest_state = self.state_history[-1]
        return latest_state.observation
    
    def calculate_performance_metrics(self) -> Dict[str, float]:
        """Calculate performance metrics for the agent."""
        if not self.reward_history:
            return {
                "avg_reward": 0.0,
                "success_rate": 0.0,
                "total_actions": 0,
                "avg_response_time": 0.0
            }
        
        # Calculate metrics
        total_reward = sum(r["reward_value"] for r in self.reward_history)
        avg_reward = total_reward / len(self.reward_history)
        
        success_count = sum(1 for r in self.reward_history if r["success"])
        success_rate = success_count / len(self.reward_history)
        
        total_actions = len(self.action_history)
        
        # Calculate average response time between actions
        if len(self.action_history) > 1:
            response_times = []
            for i in range(1, len(self.action_history)):
                response_time = self.action_history[i].timestamp - self.action_history[i-1].timestamp
                response_times.append(response_time)
            avg_response_time = sum(response_times) / len(response_times)
        else:
            avg_response_time = 0.0
        
        return {
            "avg_reward": avg_reward,
            "success_rate": success_rate,
            "total_actions": total_actions,
            "avg_response_time": avg_response_time,
            "total_reward": total_reward
        }
    
    @abstractmethod
    def execute_task(self, task_input: Any) -> Any:
        """Execute a task (to be implemented by subclasses)."""
        pass
    
    def execute_task_with_tracking(self, 
                                  task_input: Any,
                                  initial_context: Dict[str, Any] = None) -> Any:
        """Execute task with automatic trajectory tracking."""
        # Start trajectory if enabled
        if self.enable_trajectory_collection:
            self.start_trajectory(initial_context or {"task_input": str(task_input)})
        
        try:
            # Execute the actual task
            result = self.execute_task(task_input)
            
            # Record success
            if self.enable_trajectory_collection:
                self.record_reward(
                    reward_value=1.0,
                    reward_components={"task_completion": 1.0},
                    success=True,
                    metadata={"result": str(result)}
                )
                
                self.end_trajectory(
                    success=True,
                    task_completed=True,
                    final_context={"result": str(result)}
                )
            
            return result
            
        except Exception as e:
            # Record failure
            if self.enable_trajectory_collection:
                self.record_reward(
                    reward_value=-1.0,
                    reward_components={"task_completion": -1.0},
                    success=False,
                    metadata={"error": str(e)}
                )
                
                self.end_trajectory(
                    success=False,
                    task_completed=False,
                    final_context={"error": str(e)},
                    error_messages=[str(e)]
                )
            
            raise
    
    def get_trajectory_summary(self) -> Dict[str, Any]:
        """Get summary of current trajectory data."""
        return {
            "agent_type": self.agent_type,
            "current_trajectory_id": self.current_trajectory_id,
            "trajectory_active": self.current_trajectory_id is not None,
            "states_recorded": len(self.state_history),
            "actions_recorded": len(self.action_history),
            "rewards_recorded": len(self.reward_history),
            "performance_metrics": self.calculate_performance_metrics()
        }

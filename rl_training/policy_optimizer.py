"""
Policy Optimizer for RL Strategy Training

This module provides policy optimization capabilities for improving
agent decision-making strategies using collected trajectory data.
"""

import json
import time
import logging
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union
import numpy as np

# Ray RLlib imports
try:
    import ray
    from ray.rllib.algorithms.ppo import PPOConfig, PPO
    from ray.rllib.algorithms.sac import SACConfig, SAC
    from ray.rllib.algorithms.dqn import DQNConfig, DQN
    from ray.rllib.policy.policy import Policy
    from ray.rllib.utils.metrics import collect_metrics
    HAS_RAY = True
except ImportError:
    HAS_RAY = False

from .training_config import TrainingConfig, OptimizationAlgorithm
from .trajectory_collector import TrajectoryCollector, TrajectoryData

logger = logging.getLogger(__name__)


@dataclass
class OptimizationResult:
    """Results from policy optimization."""
    optimization_id: str
    algorithm: str
    start_time: datetime
    end_time: datetime
    
    # Training metrics
    total_iterations: int
    final_reward: float
    best_reward: float
    convergence_iteration: Optional[int]
    
    # Performance metrics
    training_time: float
    avg_episode_length: float
    success_rate: float
    
    # Model information
    model_path: str
    checkpoint_path: str
    
    # Hyperparameters used
    hyperparameters: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        result = asdict(self)
        result['start_time'] = self.start_time.isoformat()
        result['end_time'] = self.end_time.isoformat()
        return result


@dataclass
class OptimizationConfig:
    """Configuration for policy optimization."""
    
    # Optimization settings
    max_iterations: int = 1000
    convergence_threshold: float = 0.01
    patience: int = 50
    
    # Evaluation settings
    eval_frequency: int = 10
    eval_episodes: int = 100
    
    # Checkpointing
    checkpoint_frequency: int = 25
    keep_checkpoints: int = 5
    
    # Early stopping
    early_stopping: bool = True
    min_improvement: float = 0.001
    
    # Resource allocation
    num_workers: int = 4
    num_gpus: float = 0.0


class PolicyOptimizer:
    """Optimizes RL policies using collected trajectory data."""
    
    def __init__(self, 
                 config: TrainingConfig,
                 trajectory_collector: TrajectoryCollector,
                 output_dir: str = "policy_optimization_output"):
        """Initialize policy optimizer."""
        if not HAS_RAY:
            raise ImportError("Ray RLlib is required for policy optimization")
        
        self.config = config
        self.trajectory_collector = trajectory_collector
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize Ray if not already done
        if not ray.is_initialized():
            ray.init(ignore_reinit_error=True)
        
        # Algorithm mapping
        self.algorithm_map = {
            OptimizationAlgorithm.PPO: (PPOConfig, PPO),
            OptimizationAlgorithm.SAC: (SACConfig, SAC),
            OptimizationAlgorithm.DQN: (DQNConfig, DQN)
        }
        
        self.current_algorithm = None
        self.optimization_history: List[OptimizationResult] = []
        
        logger.info(f"PolicyOptimizer initialized with {config.algorithm.value} algorithm")
    
    def optimize_policy(self,
                       agent_type: str,
                       optimization_config: OptimizationConfig = None) -> OptimizationResult:
        """Optimize policy for specific agent type."""
        if optimization_config is None:
            optimization_config = OptimizationConfig()
        
        start_time = datetime.now()
        optimization_id = f"opt_{agent_type}_{int(time.time())}"
        
        logger.info(f"Starting policy optimization {optimization_id} for {agent_type}")
        
        # Get training trajectories
        trajectories = self.trajectory_collector.get_trajectories(
            agent_type=agent_type,
            success_only=False,  # Include both successful and failed trajectories
            max_trajectories=self.config.min_trajectories_required * 2
        )
        
        if len(trajectories) < self.config.min_trajectories_required:
            raise ValueError(f"Insufficient trajectories: {len(trajectories)} < {self.config.min_trajectories_required}")
        
        # Prepare training data
        training_data = self._prepare_training_data(trajectories)
        
        # Configure algorithm
        algorithm_config, algorithm_class = self.algorithm_map[self.config.algorithm]
        rl_config = self._configure_algorithm(algorithm_config, optimization_config)
        
        # Build algorithm
        algorithm = rl_config.build()
        self.current_algorithm = algorithm
        
        # Training loop
        best_reward = float('-inf')
        convergence_iteration = None
        no_improvement_count = 0
        
        training_metrics = []
        
        try:
            for iteration in range(optimization_config.max_iterations):
                # Train one iteration
                result = algorithm.train()
                
                # Extract metrics
                episode_reward_mean = result.get('episode_reward_mean', 0.0)
                episode_len_mean = result.get('episode_len_mean', 0.0)
                
                training_metrics.append({
                    'iteration': iteration,
                    'reward': episode_reward_mean,
                    'episode_length': episode_len_mean,
                    'timestamp': time.time()
                })
                
                # Check for improvement
                if episode_reward_mean > best_reward + optimization_config.min_improvement:
                    best_reward = episode_reward_mean
                    no_improvement_count = 0
                    
                    # Save best model
                    best_checkpoint = algorithm.save(str(self.output_dir / f"{optimization_id}_best"))
                    logger.info(f"New best reward: {best_reward:.4f} at iteration {iteration}")
                else:
                    no_improvement_count += 1
                
                # Check convergence
                if len(training_metrics) >= 10:
                    recent_rewards = [m['reward'] for m in training_metrics[-10:]]
                    if np.std(recent_rewards) < optimization_config.convergence_threshold:
                        convergence_iteration = iteration
                        logger.info(f"Converged at iteration {iteration}")
                        break
                
                # Early stopping
                if (optimization_config.early_stopping and 
                    no_improvement_count >= optimization_config.patience):
                    logger.info(f"Early stopping at iteration {iteration} (no improvement for {no_improvement_count} iterations)")
                    break
                
                # Periodic checkpointing
                if iteration % optimization_config.checkpoint_frequency == 0:
                    checkpoint_path = algorithm.save(str(self.output_dir / f"{optimization_id}_iter_{iteration}"))
                    logger.debug(f"Saved checkpoint at iteration {iteration}: {checkpoint_path}")
                
                # Periodic evaluation
                if iteration % optimization_config.eval_frequency == 0:
                    eval_metrics = self._evaluate_policy(algorithm, optimization_config.eval_episodes)
                    logger.info(f"Iteration {iteration}: reward={episode_reward_mean:.4f}, eval_success_rate={eval_metrics['success_rate']:.3f}")
        
        except KeyboardInterrupt:
            logger.info("Training interrupted by user")
        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise
        
        # Final evaluation
        final_eval = self._evaluate_policy(algorithm, optimization_config.eval_episodes * 2)
        
        # Save final model
        final_checkpoint = algorithm.save(str(self.output_dir / f"{optimization_id}_final"))
        
        end_time = datetime.now()
        training_time = (end_time - start_time).total_seconds()
        
        # Create optimization result
        result = OptimizationResult(
            optimization_id=optimization_id,
            algorithm=self.config.algorithm.value,
            start_time=start_time,
            end_time=end_time,
            total_iterations=len(training_metrics),
            final_reward=training_metrics[-1]['reward'] if training_metrics else 0.0,
            best_reward=best_reward,
            convergence_iteration=convergence_iteration,
            training_time=training_time,
            avg_episode_length=final_eval['avg_episode_length'],
            success_rate=final_eval['success_rate'],
            model_path=final_checkpoint,
            checkpoint_path=best_checkpoint if 'best_checkpoint' in locals() else final_checkpoint,
            hyperparameters=self.config.hyperparameters.__dict__
        )
        
        # Save optimization result
        self._save_optimization_result(result, training_metrics)
        self.optimization_history.append(result)
        
        logger.info(f"Optimization completed: {optimization_id}")
        logger.info(f"Final reward: {result.final_reward:.4f}, Best reward: {result.best_reward:.4f}")
        logger.info(f"Success rate: {result.success_rate:.3f}, Training time: {training_time:.1f}s")
        
        return result
    
    def _prepare_training_data(self, trajectories: List[TrajectoryData]) -> Dict[str, Any]:
        """Prepare trajectory data for RL training."""
        # Convert trajectories to format suitable for offline RL
        observations = []
        actions = []
        rewards = []
        dones = []
        
        for trajectory in trajectories:
            # Extract observations (states)
            traj_obs = [state.observation for state in trajectory.states]
            observations.extend(traj_obs)
            
            # Extract actions
            traj_actions = [action.action_value for action in trajectory.actions]
            actions.extend(traj_actions)
            
            # Extract rewards
            traj_rewards = [reward.reward_value for reward in trajectory.rewards]
            rewards.extend(traj_rewards)
            
            # Mark episode boundaries
            traj_dones = [False] * (len(traj_rewards) - 1) + [True]
            dones.extend(traj_dones)
        
        return {
            'observations': np.array(observations),
            'actions': np.array(actions),
            'rewards': np.array(rewards),
            'dones': np.array(dones),
            'num_trajectories': len(trajectories),
            'total_steps': len(observations)
        }
    
    def _configure_algorithm(self, 
                           algorithm_config_class,
                           optimization_config: OptimizationConfig):
        """Configure RL algorithm with hyperparameters."""
        from rl_env.minimal_blender_task_env import MinimalBlenderTaskEnv
        
        config = (
            algorithm_config_class()
            .environment(MinimalBlenderTaskEnv)
            .framework("torch")
            .training(
                lr=self.config.hyperparameters.learning_rate,
                gamma=self.config.hyperparameters.gamma,
                lambda_=self.config.hyperparameters.lambda_,
                train_batch_size=self.config.hyperparameters.train_batch_size,
                num_sgd_iter=self.config.hyperparameters.num_sgd_iter,
                entropy_coeff=self.config.hyperparameters.entropy_coeff,
                vf_loss_coeff=self.config.hyperparameters.vf_loss_coeff,
                clip_param=self.config.hyperparameters.clip_param
            )
            .resources(
                num_gpus=optimization_config.num_gpus,
                num_cpus_per_worker=1
            )
            .rollouts(
                num_rollout_workers=optimization_config.num_workers
            )
        )
        
        return config
    
    def _evaluate_policy(self, algorithm, num_episodes: int) -> Dict[str, float]:
        """Evaluate policy performance."""
        from rl_env.minimal_blender_task_env import MinimalBlenderTaskEnv
        
        env = MinimalBlenderTaskEnv()
        
        episode_rewards = []
        episode_lengths = []
        success_count = 0
        
        for _ in range(num_episodes):
            obs, _ = env.reset()
            episode_reward = 0
            episode_length = 0
            done = False
            
            while not done:
                action = algorithm.compute_single_action(obs)
                obs, reward, terminated, truncated, info = env.step(action)
                episode_reward += reward
                episode_length += 1
                done = terminated or truncated
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            
            # Check if episode was successful (simplified)
            if episode_reward > 0:
                success_count += 1
        
        return {
            'avg_reward': np.mean(episode_rewards),
            'std_reward': np.std(episode_rewards),
            'avg_episode_length': np.mean(episode_lengths),
            'success_rate': success_count / num_episodes
        }
    
    def _save_optimization_result(self, 
                                 result: OptimizationResult,
                                 training_metrics: List[Dict[str, Any]]) -> None:
        """Save optimization results and metrics."""
        # Save result summary
        result_path = self.output_dir / f"{result.optimization_id}_result.json"
        with open(result_path, 'w') as f:
            json.dump(result.to_dict(), f, indent=2)
        
        # Save detailed training metrics
        metrics_path = self.output_dir / f"{result.optimization_id}_metrics.json"
        with open(metrics_path, 'w') as f:
            json.dump(training_metrics, f, indent=2)
        
        logger.info(f"Saved optimization results to {result_path}")
    
    def get_best_model(self, agent_type: str) -> Optional[str]:
        """Get path to best model for agent type."""
        agent_results = [r for r in self.optimization_history 
                        if agent_type in r.optimization_id]
        
        if not agent_results:
            return None
        
        best_result = max(agent_results, key=lambda r: r.best_reward)
        return best_result.checkpoint_path
    
    def cleanup_old_checkpoints(self, keep_latest: int = 5) -> None:
        """Clean up old checkpoint files."""
        checkpoint_files = list(self.output_dir.glob("*_iter_*"))
        checkpoint_files.sort(key=lambda p: p.stat().st_mtime, reverse=True)
        
        # Keep only the latest checkpoints
        for checkpoint_file in checkpoint_files[keep_latest:]:
            try:
                checkpoint_file.unlink()
                logger.debug(f"Removed old checkpoint: {checkpoint_file}")
            except Exception as e:
                logger.warning(f"Failed to remove checkpoint {checkpoint_file}: {e}")

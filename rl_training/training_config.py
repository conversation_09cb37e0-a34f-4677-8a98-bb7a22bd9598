"""
Training Configuration for RL Strategy Training

This module defines configuration classes for RL training, including
hyperparameters, evaluation settings, and training modes.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, Any, Optional, List
import json
from pathlib import Path


class TrainingMode(Enum):
    """Training modes for RL strategy optimization."""
    OFFLINE = "offline"
    ONLINE = "online"
    MIXED = "mixed"
    EVALUATION = "evaluation"


class OptimizationAlgorithm(Enum):
    """Supported RL optimization algorithms."""
    PPO = "ppo"
    SAC = "sac"
    DQN = "dqn"
    A3C = "a3c"


@dataclass
class HyperparameterConfig:
    """Hyperparameter configuration for RL training."""
    
    # Learning parameters
    learning_rate: float = 0.0003
    gamma: float = 0.99
    lambda_: float = 0.95
    
    # Training parameters
    train_batch_size: int = 1000
    sgd_minibatch_size: int = 128
    num_sgd_iter: int = 10
    
    # Network architecture
    hidden_layers: List[int] = field(default_factory=lambda: [256, 256])
    activation: str = "relu"
    
    # Exploration
    exploration_config: Dict[str, Any] = field(default_factory=lambda: {
        "type": "EpsilonGreedy",
        "initial_epsilon": 1.0,
        "final_epsilon": 0.02,
        "epsilon_timesteps": 10000
    })
    
    # Regularization
    entropy_coeff: float = 0.01
    vf_loss_coeff: float = 0.5
    clip_param: float = 0.3


@dataclass
class EvaluationConfig:
    """Configuration for model evaluation."""
    
    # Evaluation frequency
    evaluation_interval: int = 10
    evaluation_duration: int = 100
    
    # Metrics to track
    metrics: List[str] = field(default_factory=lambda: [
        "episode_reward_mean",
        "episode_len_mean", 
        "policy_entropy",
        "vf_explained_var"
    ])
    
    # Performance thresholds
    min_reward_threshold: float = 0.5
    convergence_threshold: float = 0.01
    patience: int = 20


@dataclass
class TrainingConfig:
    """Main training configuration."""
    
    # Basic settings
    experiment_name: str = "rl_strategy_training"
    training_mode: TrainingMode = TrainingMode.OFFLINE
    algorithm: OptimizationAlgorithm = OptimizationAlgorithm.PPO
    
    # Training parameters
    num_workers: int = 4
    num_gpus: float = 0.0
    max_training_iterations: int = 1000
    checkpoint_frequency: int = 50
    
    # Data settings
    trajectory_data_path: Optional[str] = None
    max_trajectory_length: int = 200
    min_trajectories_required: int = 100
    
    # Hyperparameters
    hyperparameters: HyperparameterConfig = field(default_factory=HyperparameterConfig)
    evaluation: EvaluationConfig = field(default_factory=EvaluationConfig)
    
    # Output settings
    output_dir: str = "rl_training_output"
    model_save_path: str = "trained_models"
    log_level: str = "INFO"
    
    # MLOps integration
    track_experiments: bool = True
    register_models: bool = True
    auto_deploy: bool = False
    
    def save(self, path: str):
        """Save configuration to JSON file."""
        config_dict = self._to_dict()
        with open(path, 'w') as f:
            json.dump(config_dict, f, indent=2)
    
    @classmethod
    def load(cls, path: str) -> 'TrainingConfig':
        """Load configuration from JSON file."""
        with open(path, 'r') as f:
            config_dict = json.load(f)
        return cls._from_dict(config_dict)
    
    def _to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, Enum):
                result[key] = value.value
            elif hasattr(value, '__dict__'):
                result[key] = value.__dict__
            else:
                result[key] = value
        return result
    
    @classmethod
    def _from_dict(cls, config_dict: Dict[str, Any]) -> 'TrainingConfig':
        """Create instance from dictionary."""
        # Convert enum values back
        if 'training_mode' in config_dict:
            config_dict['training_mode'] = TrainingMode(config_dict['training_mode'])
        if 'algorithm' in config_dict:
            config_dict['algorithm'] = OptimizationAlgorithm(config_dict['algorithm'])
        
        # Convert nested configs
        if 'hyperparameters' in config_dict:
            config_dict['hyperparameters'] = HyperparameterConfig(**config_dict['hyperparameters'])
        if 'evaluation' in config_dict:
            config_dict['evaluation'] = EvaluationConfig(**config_dict['evaluation'])
        
        return cls(**config_dict)


# Default configurations for different scenarios
DEFAULT_OFFLINE_CONFIG = TrainingConfig(
    experiment_name="offline_strategy_training",
    training_mode=TrainingMode.OFFLINE,
    max_training_iterations=500,
    hyperparameters=HyperparameterConfig(
        learning_rate=0.0001,
        train_batch_size=2000
    )
)

DEFAULT_ONLINE_CONFIG = TrainingConfig(
    experiment_name="online_strategy_training", 
    training_mode=TrainingMode.ONLINE,
    max_training_iterations=1000,
    hyperparameters=HyperparameterConfig(
        learning_rate=0.0003,
        train_batch_size=500
    )
)

DEFAULT_EVALUATION_CONFIG = TrainingConfig(
    experiment_name="strategy_evaluation",
    training_mode=TrainingMode.EVALUATION,
    max_training_iterations=100,
    evaluation=EvaluationConfig(
        evaluation_interval=1,
        evaluation_duration=50
    )
)
